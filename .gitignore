# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Virtual Environments
Comfyvenv/
Backvenv/
venv/
env/
.env

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Database
*.db
*.sqlite
*.sqlite3
data/embeddings.db

# Temporary files
temp/
tmp/
*.tmp

# ComfyUI specific
ComfyUI_windows_portable/
output/
models/
*.ckpt
*.safetensors
*.pt
*.pth

# Large model files
*.bin
*.h5
*.onnx

# API Keys and secrets
config/secrets.json
*.key
api_keys.txt

# Build outputs
.next/
out/
build/
dist/

# Cache
.cache/
.npm/
.yarn/

# Test coverage
coverage/
.nyc_output/

# Backup files
backups/
*.backup
*.bak

# Windows specific
*.lnk
desktop.ini

# Package manager lock files (include in repo)
# package-lock.json
# yarn.lock
