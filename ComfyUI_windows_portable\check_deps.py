import sys
try:
    import xformers
    print(f"xformers version: {xformers.__version__}")
    print("xformers is available and working")
except ImportError as e:
    print(f"xformers not available: {str(e)}")
    print("This is OK - we can run without xformers")

try:
    import torch
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
except Exception as e:
    print(f"PyTorch error: {e}")
