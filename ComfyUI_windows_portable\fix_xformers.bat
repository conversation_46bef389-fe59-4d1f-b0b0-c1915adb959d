@echo off
echo 🛠️ ComfyUI xformers Error Fix
echo ===============================

echo.
echo 📍 Step 1: Checking current PyTorch installation...
cd /d "g:\PORT_COMFY_front\ComfyUI_windows_portable"
python_embeded\python.exe -c "import torch; print('PyTorch:', torch.__version__); print('CUDA:', torch.cuda.is_available())"

echo.
echo 📍 Step 2: Fixing xformers compatibility...
python_embeded\python.exe -m pip uninstall xformers -y
python_embeded\python.exe -m pip install xformers --index-url https://download.pytorch.org/whl/cu128

echo.
echo 📍 Step 3: Testing ComfyUI startup...
python_embeded\python.exe -c "import ComfyUI.main; print('ComfyUI imports successfully')"

if %ERRORLEVEL% EQU 0 (
    echo ✅ ComfyUI should now start without errors
    echo.
    echo Run this to start ComfyUI:
    echo   run_nvidia_gpu.bat
) else (
    echo ❌ Still having issues. Use safe mode:
    echo   run_nvidia_gpu_safe.bat
)

echo.
pause
