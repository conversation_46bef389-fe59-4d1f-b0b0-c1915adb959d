@echo off
echo 🔧 ComfyUI Startup with xformers Error Fix
echo ==========================================

cd /d "g:\PORT_COMFY_front\ComfyUI_windows_portable"

echo.
echo 📍 Attempting to start ComfyUI normally...
python_embeded\python.exe -s ComfyUI\main.py --windows-standalone-build --disable-auto-launch

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ⚠️  Normal startup failed, trying without xformers...
    echo 📍 Starting in safe mode (xformers disabled)...
    python_embeded\python.exe -s ComfyUI\main.py --windows-standalone-build --disable-auto-launch --disable-xformers
)

echo.
echo ============================================================
echo   ComfyUI startup completed
echo   Custom Frontend: http://localhost:3003
echo ============================================================
pause
