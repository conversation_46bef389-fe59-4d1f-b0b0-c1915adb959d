@echo off
echo 🔧 ComfyUI Startup - Smart xformers Detection
echo ==============================================

cd /d "g:\PORT_COMFY_front\ComfyUI_windows_portable"

echo.
echo 📍 Checking xformers compatibility...
python_embeded\python.exe -c "import torch, xformers; print('PyTorch:', torch.__version__); print('xformers:', xformers.__version__); print('Compatible:', torch.__version__.split('+')[0] in xformers.__version__ or '2.8' in xformers.__version__)"

if %ERRORLEVEL% EQU 0 (
    echo ✅ xformers is compatible, starting with acceleration...
    python_embeded\python.exe -s ComfyUI\main.py --windows-standalone-build --disable-auto-launch
) else (
    echo ⚠️  xformers compatibility issue detected, starting without xformers...
    python_embeded\python.exe -s ComfyUI\main.py --windows-standalone-build --disable-auto-launch --disable-xformers
)

echo.
echo ============================================================
echo   ComfyUI is running on port 8188
echo   Custom Frontend: http://localhost:3003
echo ============================================================
pause
