import torch
import sys

print("=" * 50)
print("🔍 SYSTEM CHECK RESULTS")
print("=" * 50)

# PyTorch version
print(f"📍 PyTorch Version: {torch.__version__}")

# CUDA availability
print(f"📍 CUDA Available: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"📍 CUDA Device Count: {torch.cuda.device_count()}")
    print(f"📍 Current Device: {torch.cuda.current_device()}")
    print(f"📍 Device Name: {torch.cuda.get_device_name(0)}")
    print(f"📍 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
else:
    print("❌ No CUDA GPU detected")

# xformers check
try:
    import xformers
    print(f"📍 xformers Version: {xformers.__version__}")
except ImportError:
    print("📍 xformers: Not installed (OK for stability)")

print("=" * 50)
print("✅ System check complete!")
print("=" * 50)
