# ComfyUI Custom Frontend

A professional, hardware-optimized frontend interface for ComfyUI with AI-powered creative features.

## Features

- **Multiple Generation Modes**: Text-to-image, Image-to-image, Inpainting, Outpainting
- **AI Creative Mode**: LLM-powered prompt generation with Ollama integration
- **Reference Image Analysis**: Use existing images as style/composition guides
- **Image Enhancement**: 4x upscaling and quality enhancement
- **Hardware Optimized**: Designed for RTX 4070 Ti SUPER + 64GB RAM

## Tech Stack

- **Frontend**: Next.js 14 + TypeScript + Tailwind CSS
- **Backend**: FastAPI + WebSocket support
- **AI Integration**: Ollama LLM (nous-hermes2-mixtral)
- **Image Processing**: ComfyUI API integration

## 🚨 Important for Developers

**Before editing UI components, read this first:**
- 📖 **[Quick Reference: UI Editing Guide](docs/QUICK_REFERENCE_UI_EDITING.md)** - Essential guide to avoid common mistakes
- 📖 **[Right Panel Architecture](docs/RIGHT_PANEL_ARCHITECTURE.md)** - Detailed documentation of the contextual menu system

**Key Point**: The main app uses `Layout.tsx`, NOT `DashboardIsolated.tsx`. Multiple attempts to edit the wrong file have failed in the past.

## Project Structure

```
comfyui_custom/
├── frontend/           # Next.js frontend application
├── backend/            # FastAPI backend server
├── docs/              # Documentation
├── configs/           # Configuration files
└── README.md          # This file
```
## IMPORTANT INFORMATION: This project will utilize the backend from the portable comfyui at G:/PORT_COMFY_front/ComfyUI_windows_portable/. However, All models are linked from L:/ComfyUI/models

## Hardware Requirements

- GPU: NVIDIA RTX 4070 Ti SUPER (16GB VRAM)
- RAM: 64GB DDR5
- Storage: Multi-drive setup (C:/, L:/, G:/)
- CUDA: 12.8+

## Getting Started

### Quick Setup
Run the automated environment setup:
```powershell
.\setup_environment.ps1
```

### Manual Setup
1. **Backend (Python 3.11 + Virtual Environment):**
   ```powershell
   cd backend
   python -m venv venv
   .\venv\Scripts\Activate.ps1
   pip install -r requirements.txt
   ```

2. **Frontend:**
   ```powershell
   cd frontend
   npm install
   ```

3. **Start Development:**
   ```powershell
   # Run both services
   npm run dev
   
   # Or run separately:
   # Backend: cd backend && .\activate_and_run.ps1
   # Frontend: cd frontend && npm run dev
   ```

For detailed setup instructions, see [ENVIRONMENT_SETUP.md](ENVIRONMENT_SETUP.md).
