# ComfyUI Custom Frontend

A professional, hardware-optimized frontend interface for ComfyUI with AI-powered creative features.

## 🎉 **Status: FULLY OPERATIONAL**

✅ **Complete end-to-end generation pipeline working**  
✅ **GPU acceleration with RTX 4070 Ti SUPER**  
✅ **Async generation with real-time polling**  
✅ **All dependency conflicts resolved**  

## Features

- **Multiple Generation Modes**: Text-to-image, Image-to-image, Inpainting, Outpainting
- **AI Creative Mode**: LLM-powered prompt generation with Ollama integration
- **Reference Image Analysis**: Use existing images as style/composition guides
- **Image Enhancement**: 4x upscaling and quality enhancement
- **Hardware Optimized**: Designed for RTX 4070 Ti SUPER + 64GB RAM
- **Async Generation**: No more frontend timeouts, real-time status updates
- **CUDA Acceleration**: Full GPU support with PyTorch 2.8.0+cu128

## Tech Stack

- **Frontend**: Next.js 14 + TypeScript + Tailwind CSS
- **Backend**: FastAPI + WebSocket support + SQLAlchemy 2.0
- **AI Integration**: Ollama LLM (nous-hermes2-mixtral)
- **Image Processing**: ComfyUI API integration
- **GPU**: PyTorch 2.8.0+cu128 with CUDA 12.8 support

## 🚨 Important for Developers

**Before editing UI components, read this first:**
- 📖 **[Quick Reference: UI Editing Guide](docs/QUICK_REFERENCE_UI_EDITING.md)** - Essential guide to avoid common mistakes
- 📖 **[Right Panel Architecture](docs/RIGHT_PANEL_ARCHITECTURE.md)** - Detailed documentation of the contextual menu system

**Key Point**: The main app uses `Layout.tsx`, NOT `DashboardIsolated.tsx`. Multiple attempts to edit the wrong file have failed in the past.

## Project Structure

```
comfyui_custom/
├── frontend/           # Next.js frontend application
├── backend/            # FastAPI backend server
├── docs/              # Documentation
├── configs/           # Configuration files
└── README.md          # This file
```
## IMPORTANT INFORMATION: This project will utilize the backend from the portable comfyui at G:/PORT_COMFY_front/ComfyUI_windows_portable/. However, All models are linked from L:/ComfyUI/models

## Hardware Requirements

- GPU: NVIDIA RTX 4070 Ti SUPER (16GB VRAM)
- RAM: 64GB DDR5
- Storage: Multi-drive setup (C:/, L:/, G:/)
- CUDA: 12.8+

## Getting Started

### 🚀 **Recommended Startup Methods**

#### **Option 1: Service Manager (Recommended)**
Complete control with menu-driven interface:
```cmd
cd g:\comfyui_Front
service_manager.bat
```

#### **Option 2: Full System Startup**
Comprehensive startup with verification:
```cmd
cd g:\comfyui_Front
START_COMPLETE_SYSTEM.bat
```

#### **Option 3: Quick Start**
Fast startup for daily use:
```cmd
cd g:\comfyui_Front
quick_start.bat
```

### 🔧 **Manual Startup (Advanced Users)**

If you prefer to start services individually:

#### 1. **ComfyUI Server**
```cmd
cd /d "g:\PORT_COMFY_front\ComfyUI_windows_portable"
python_embeded\python.exe -s ComfyUI\main.py --windows-standalone-build --disable-auto-launch --disable-xformers
```

#### 2. **Backend API**
```cmd
cd /d "g:\comfyui_Front"
call Comfyvenv\Scripts\activate.bat
python backend/main.py
```

#### 3. **Frontend Interface**
```cmd
cd /d "g:\comfyui_Front\frontend"
npm run dev
```

### 🌐 **Access URLs**
- **Frontend**: <http://localhost:3000>
- **Backend API**: <http://localhost:8000>
- **ComfyUI**: <http://localhost:8188>

### 📋 **Service Management**
- **Start All**: Use `service_manager.bat` or `START_COMPLETE_SYSTEM.bat`
- **Stop All**: Use service manager option 3, or close individual command windows
- **Restart Individual**: Use service manager option 7
- **Check Status**: Use `check_system.bat` or service manager option 4

## 📦 **Backup & Version Control**

### **Create Backup**
```cmd
cd g:\comfyui_Front
create_backup.bat
```

### **Initialize Git Repository**
```cmd
cd g:\comfyui_Front
init_git.bat
```

## 📚 **Documentation**

- **[Setup Confirmed](SETUP_CONFIRMED.md)** - Current operational status
- **[Changelog](CHANGELOG.md)** - All fixes and improvements
- **[Quick Reference: UI Editing](docs/QUICK_REFERENCE_UI_EDITING.md)** - UI development guide
