@echo off
title ComfyUI Complete System Startup with Logging
color 0A

REM Initialize logging system
echo.
echo 🔄 Initializing centralized logging system...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_system_start "Complete System Startup Initiated"

echo.
echo 🚀 ComfyUI Complete System Startup
echo ===================================
echo.

REM Check and start logging session
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "STARTUP" "Starting complete system verification and launch"

REM Check if Python is available
echo 🔍 Checking system requirements...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found in PATH
    python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_error "STARTUP" "Python not found in PATH"
    goto :error_exit
)

echo ✅ Python found
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "STARTUP" "Python verification successful"

REM Check port availability first
echo.
echo 🔍 Checking port availability...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "PORT_CHECK" "Starting port availability check"

call check_ports.bat
if errorlevel 1 (
    echo.
    echo ⚠️  Port conflicts detected. Running port cleanup...
    python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_warning "PORT_CHECK" "Port conflicts detected, initiating cleanup"
    call emergency_port_cleanup.bat
    
    echo.
    echo 🔄 Rechecking ports after cleanup...
    call check_ports.bat
    if errorlevel 1 (
        echo ❌ Unable to resolve port conflicts
        python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_error "PORT_CHECK" "Unable to resolve port conflicts after cleanup"
        goto :error_exit
    )
)

echo ✅ All required ports available
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "PORT_CHECK" "All required ports available"

REM Start ComfyUI Backend
echo.
echo 🔄 Starting ComfyUI Backend Server...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "COMFYUI" "Starting ComfyUI backend server"

cd /d "g:\comfyui_Front"
start "ComfyUI Backend" cmd /k "python backend\main.py"

echo ⏳ Waiting for backend to initialize...
timeout /t 5 /nobreak >nul

REM Verify backend is running
echo 🔍 Verifying backend server...
python test_backend_endpoint.py >nul 2>&1
if errorlevel 1 (
    echo ❌ Backend server failed to start properly
    python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_error "COMFYUI" "Backend server failed to start or respond"
    goto :error_exit
)

echo ✅ Backend server running successfully
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "COMFYUI" "Backend server started and responding"

REM Start Frontend Development Server
echo.
echo 🔄 Starting Frontend Development Server...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "FRONTEND" "Starting frontend development server"

cd /d "g:\comfyui_Front\frontend"
start "Frontend Dev Server" cmd /k "npm run dev"

echo ⏳ Waiting for frontend to initialize...
timeout /t 8 /nobreak >nul

echo.
echo ✅ Complete system startup finished!
echo.
echo 📋 System Status:
echo   🟢 ComfyUI Backend: Running on port 8188
echo   🟢 Frontend Dev Server: Running on port 5173
echo.
echo 🌐 Access your application at:
echo   Frontend: http://localhost:5173
echo   Backend API: http://localhost:8188
echo.
echo 📊 Check errors anytime with: check_errors_for_claude.bat
echo.

python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "STARTUP" "Complete system startup successful - all services running"

echo Press any key to open frontend in browser...
pause >nul
start "" "http://localhost:5173"
goto :end

:error_exit
echo.
echo ❌ System startup failed!
echo 📊 Check error details with: check_errors_for_claude.bat
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_error "STARTUP" "Complete system startup failed"
echo.
pause
exit /b 1

:end
echo.
echo System startup monitoring complete.
pause