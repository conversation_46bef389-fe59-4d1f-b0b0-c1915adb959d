@echo off
title ComfyUI Custom Frontend - Master Startup
color 0B
echo.
echo ========================================================================
echo                    🚀 ComfyUI Custom Frontend v1.1.0
echo                         MASTER STARTUP SCRIPT
echo ========================================================================
echo.
echo ✅ System Status: FULLY OPERATIONAL
echo 🔧 GPU: RTX 4070 Ti SUPER with PyTorch 2.8.0+cu128
echo 🎯 Mode: Production-ready with async generation
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 🔐 Running with Administrator privileges
) else (
    echo ⚠️  Note: Not running as Administrator (this is usually fine)
)

echo.
echo 📋 This script will start all three services:
echo    1. ComfyUI Server (with CUDA acceleration, no xformers)
echo    2. Backend API Server (FastAPI with async support)
echo    3. Frontend Development Server (Next.js with real-time polling)
echo.

set /p confirm="🚀 Start the complete system? (y/n): "
if /i not "%confirm%"=="y" (
    echo ❌ Startup cancelled by user.
    goto :end
)

echo.
echo ========================================================================
echo                            🔍 SYSTEM VERIFICATION
echo ========================================================================

echo.
echo 📍 Step 1/6: Verifying project structure...
if not exist "g:\comfyui_Front\backend" (
    echo ❌ ERROR: Backend directory not found at g:\comfyui_Front\backend
    goto :error
)
if not exist "g:\comfyui_Front\frontend" (
    echo ❌ ERROR: Frontend directory not found at g:\comfyui_Front\frontend
    goto :error
)
if not exist "g:\PORT_COMFY_front\ComfyUI_windows_portable" (
    echo ❌ ERROR: ComfyUI installation not found at g:\PORT_COMFY_front\ComfyUI_windows_portable
    goto :error
)
echo ✅ Project structure verified

echo.
echo 📍 Step 2/6: Checking Python virtual environment...
if not exist "g:\comfyui_Front\Comfyvenv\Scripts\activate.bat" (
    echo ❌ ERROR: Backend virtual environment not found
    echo 💡 Please run: python -m venv Comfyvenv
    goto :error
)
echo ✅ Backend virtual environment found

echo.
echo 📍 Step 3/6: Checking ComfyUI Python...
if not exist "g:\PORT_COMFY_front\ComfyUI_windows_portable\python_embeded\python.exe" (
    echo ❌ ERROR: ComfyUI Python not found
    goto :error
)
echo ✅ ComfyUI Python environment found

echo.
echo 📍 Step 4/6: Checking Node.js dependencies...
cd /d "g:\comfyui_Front\frontend"
if not exist "node_modules" (
    echo ⚠️  Node modules not found, installing...
    npm install
    if errorlevel 1 (
        echo ❌ ERROR: Failed to install Node.js dependencies
        goto :error
    )
)
echo ✅ Node.js dependencies verified

echo.
echo 📍 Step 5/6: Verifying PyTorch CUDA...
cd /d "g:\PORT_COMFY_front\ComfyUI_windows_portable"
python_embeded\python.exe -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA Available: {torch.cuda.is_available()}'); print(f'GPU: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else \"None\"}')" 2>nul
if errorlevel 1 (
    echo ⚠️  PyTorch verification failed, but continuing...
) else (
    echo ✅ PyTorch CUDA verified
)

echo.
echo 📍 Step 6/6: Checking backend dependencies...
cd /d "g:\comfyui_Front"
call Comfyvenv\Scripts\activate.bat
python -c "import fastapi, sqlalchemy; print('✅ Core backend dependencies available')" 2>nul
if errorlevel 1 (
    echo ⚠️  Installing missing backend dependencies...
    pip install -r backend\requirements.txt
)
echo ✅ Backend dependencies verified

echo.
echo ========================================================================
echo                           🚀 STARTING SERVICES
echo ========================================================================

echo.
echo 📍 Service 1/3: Starting ComfyUI Server...
echo ⏳ Initializing GPU-accelerated ComfyUI (this may take 30-60 seconds)
cd /d "g:\PORT_COMFY_front\ComfyUI_windows_portable"
start "ComfyUI Server" cmd /k "title ComfyUI Server && echo Starting ComfyUI with CUDA acceleration... && python_embeded\python.exe -s ComfyUI\main.py --windows-standalone-build --disable-auto-launch --disable-xformers --listen 127.0.0.1 --port 8188"

echo ✅ ComfyUI Server starting in separate window
echo 🌐 ComfyUI will be available at: http://localhost:8188

echo.
echo ⏳ Waiting 15 seconds for ComfyUI to initialize...
timeout /t 15 /nobreak >nul

echo.
echo 📍 Service 2/3: Starting Backend API Server...
cd /d "g:\comfyui_Front"
call Comfyvenv\Scripts\activate.bat
start "Backend API" cmd /k "title Backend API Server && echo Starting FastAPI backend with async support... && python backend\main.py"

echo ✅ Backend API Server starting in separate window
echo 🌐 Backend API will be available at: http://localhost:8000

echo.
echo ⏳ Waiting 5 seconds for backend to initialize...
timeout /t 5 /nobreak >nul

echo.
echo 📍 Service 3/3: Starting Frontend Development Server...
cd /d "g:\comfyui_Front\frontend"
start "Frontend UI" cmd /k "title Frontend Development Server && echo Starting Next.js frontend with polling support... && npm run dev"

echo ✅ Frontend Development Server starting in separate window
echo 🌐 Frontend will be available at: http://localhost:3000

echo.
echo ⏳ Waiting 10 seconds for frontend to initialize...
timeout /t 10 /nobreak >nul

echo.
echo ========================================================================
echo                           ✅ STARTUP COMPLETE!
echo ========================================================================
echo.
echo 🎉 All services are now running!
echo.
echo 🌐 Service URLs:
echo    Frontend UI:    http://localhost:3000
echo    Backend API:    http://localhost:8000  
echo    ComfyUI Server: http://localhost:8188
echo.
echo 📊 Service Windows:
echo    - ComfyUI Server: Running in separate command window
echo    - Backend API: Running in separate command window  
echo    - Frontend UI: Running in separate command window
echo.
echo 🔧 Features Available:
echo    ✅ Text-to-Image generation with GPU acceleration
echo    ✅ Async generation with real-time polling (no timeouts)
echo    ✅ Image display and download
echo    ✅ System monitoring and error handling
echo.
echo 🚨 Important Notes:
echo    - Keep all three command windows open
echo    - Frontend polling checks status every 2 seconds
echo    - GPU acceleration active (xformers disabled for stability)
echo    - Generation typically takes 10-30 seconds depending on settings
echo.
echo ⚡ Quick Test:
echo    1. Open http://localhost:3000 in your browser
echo    2. Enter a text prompt (e.g., "a futuristic city")
echo    3. Click Generate Image
echo    4. Watch real-time progress updates
echo.
echo 🛑 To Stop Services:
echo    Close each command window individually, or press Ctrl+C in each
echo.

:success
echo 📍 Press any key to open the frontend in your default browser...
pause >nul
start http://localhost:3000
goto :end

:error
echo.
echo ========================================================================
echo                              ❌ STARTUP FAILED
echo ========================================================================
echo.
echo 🔧 Common Solutions:
echo    1. Ensure all virtual environments are set up correctly
echo    2. Check that all dependencies are installed
echo    3. Verify ComfyUI portable installation is intact
echo    4. Run Windows as Administrator if needed
echo.
echo 📚 Documentation:
echo    - Check SETUP_CONFIRMED.md for detailed setup info
echo    - Review CHANGELOG.md for recent fixes
echo    - Use check_system.bat for system verification
echo.

:end
echo.
echo Press any key to exit...
pause >nul
exit /b
