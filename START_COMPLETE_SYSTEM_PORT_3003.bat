@echo off
echo =====================================
echo Starting Complete ComfyUI System
echo Frontend: http://localhost:3003
echo Backend API: http://localhost:8000
echo =====================================

cd /d "g:\comfyui_Front"

REM Kill any existing processes on target ports
echo [INFO] Checking for existing processes on ports 3003 and 8000...
netstat -ano | findstr :3003 >nul 2>&1
if %errorlevel% equ 0 (
    echo [INFO] Found process on port 3003, terminating...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3003') do taskkill /PID %%a /F >nul 2>&1
)

netstat -ano | findstr :8000 >nul 2>&1
if %errorlevel% equ 0 (
    echo [INFO] Found process on port 8000, terminating...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8000') do taskkill /PID %%a /F >nul 2>&1
)

REM Wait for ports to be released
timeout /t 2 >nul

echo.
echo [STEP 1/5] Activating Python virtual environment for backend...
if exist "Comfyvenv\Scripts\activate" (
    call "Comfyvenv\Scripts\activate"
    echo [OK] Python virtual environment activated
) else (
    echo [ERROR] Virtual environment not found at Comfyvenv\Scripts\activate
    echo Please ensure the virtual environment is properly set up
    pause
    exit /b 1
)

echo.
echo [STEP 2/5] Starting ComfyUI Backend Server...
echo Backend will start on http://localhost:8000
echo.

REM Start backend server in a new window with proper environment
start "ComfyUI Backend Server" cmd /k "cd /d "g:\comfyui_Front" && Comfyvenv\Scripts\activate && cd backend && python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload"

REM Wait for backend to start
echo [INFO] Waiting for backend server to initialize...
timeout /t 5 >nul

REM Check if backend is responding
echo [INFO] Testing backend connection...
:check_backend
curl -s "http://localhost:8000/health" >nul 2>&1
if %errorlevel% neq 0 (
    echo [INFO] Backend not ready yet, waiting...
    timeout /t 2 >nul
    goto check_backend
)
echo [OK] Backend server is responding

echo.
echo [STEP 3/5] Starting ComfyUI Core Service...
echo.

REM Check if ComfyUI directory exists
if not exist "g:\PORT_COMFY_front\ComfyUI_windows_portable" (
    echo [ERROR] ComfyUI directory not found at g:\PORT_COMFY_front\ComfyUI_windows_portable
    echo Please ensure ComfyUI is properly installed
    pause
    exit /b 1
)

REM Start ComfyUI in a new window
start "ComfyUI Core Service" cmd /k "cd /d "g:\PORT_COMFY_front\ComfyUI_windows_portable" && run_nvidia_gpu.bat"

REM Wait for ComfyUI to start
echo [INFO] Waiting for ComfyUI core service to initialize...
timeout /t 10 >nul

echo.
echo [STEP 4/5] Installing frontend dependencies and starting development server...
echo Frontend will start on http://localhost:3003
echo.

REM Change to frontend directory
cd frontend

REM Check if node_modules exists, install if not
if not exist "node_modules" (
    echo [INFO] Installing frontend dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install frontend dependencies
        pause
        exit /b 1
    )
    echo [OK] Frontend dependencies installed
)

REM Start frontend development server in a new window
start "ComfyUI Frontend Development Server" cmd /k "cd /d "g:\comfyui_Front\frontend" && npm run dev"

echo.
echo [STEP 5/5] Waiting for all services to be ready...
timeout /t 8 >nul

REM Check if frontend is responding
echo [INFO] Testing frontend connection...
:check_frontend
curl -s "http://localhost:3003" >nul 2>&1
if %errorlevel% neq 0 (
    echo [INFO] Frontend not ready yet, waiting...
    timeout /t 3 >nul
    goto check_frontend
)
echo [OK] Frontend server is responding

echo.
echo =====================================
echo SUCCESS: All services are running!
echo =====================================
echo.
echo Backend API:      http://localhost:8000
echo Frontend UI:      http://localhost:3003
echo ComfyUI Core:     Running in background
echo.
echo API Endpoints:
echo - Health Check:   http://localhost:8000/health
echo - Generation:     http://localhost:8000/generation/start
echo - Active Jobs:    http://localhost:8000/generation/active
echo - WebSocket:      ws://localhost:8000/ws
echo.
echo [INFO] Opening browser to frontend interface...
timeout /t 2 >nul

REM Open browser to the frontend
start "" "http://localhost:3003"

echo.
echo System is fully operational!
echo.
echo To stop all services:
echo 1. Close this window
echo 2. Close the Backend Server window
echo 3. Close the Frontend Development Server window
echo 4. Close the ComfyUI Core Service window
echo.
echo Press any key to keep this monitor window open...
pause >nul

echo.
echo [INFO] System monitoring active...
echo You can safely close this window - services will continue running
echo.
pause
