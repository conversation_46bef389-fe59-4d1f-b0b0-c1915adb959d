@echo off
title Stopping All ComfyUI Services
color 0C

REM Initialize logging
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "SHUTDOWN" "Service shutdown initiated" >nul 2>&1

echo ================================================================
echo   🛑 Stopping All ComfyUI Project Services
echo ================================================================

echo.
echo 🔍 Identifying running services...

REM Log shutdown start
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "SHUTDOWN" "Beginning comprehensive service shutdown"

REM Stop by window title first (more precise)
echo 🛑 Stopping Frontend Development Server...
taskkill /f /fi "windowtitle eq Frontend Dev Server" >nul 2>&1
if not errorlevel 1 (
    echo   ✅ Frontend server stopped
    python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "FRONTEND" "Frontend development server stopped"
) else (
    echo   ℹ️  Frontend server was not running
)

echo 🛑 Stopping ComfyUI Backend...
taskkill /f /fi "windowtitle eq ComfyUI Backend" >nul 2>&1
if not errorlevel 1 (
    echo   ✅ ComfyUI backend stopped
    python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "COMFYUI" "ComfyUI backend server stopped"
) else (
    echo   ℹ️  ComfyUI backend was not running
)

echo.
echo 🧹 Cleaning up remaining processes...

REM Kill processes that might be occupying our ports
echo 🔍 Checking for processes on port 8188...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8188') do (
    taskkill /f /pid %%a >nul 2>&1
    if not errorlevel 1 (
        echo   ✅ Stopped process on port 8188 (PID: %%a)
        python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "PORT_CLEANUP" "Stopped process on port 8188, PID: %%a"
    )
)

echo 🔍 Checking for processes on port 5173...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5173') do (
    taskkill /f /pid %%a >nul 2>&1
    if not errorlevel 1 (
        echo   ✅ Stopped process on port 5173 (PID: %%a)
        python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "PORT_CLEANUP" "Stopped process on port 5173, PID: %%a"
    )
)

echo.
echo 🧹 General cleanup of project processes...

echo   Stopping Node.js processes (Frontend)...
taskkill /F /IM node.exe /T >nul 2>&1
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "CLEANUP" "Node.js processes terminated"

echo   Stopping Python processes (ComfyUI, Backend)...
taskkill /F /IM python.exe /T >nul 2>&1
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "CLEANUP" "Python processes terminated"

echo   Stopping Uvicorn processes (Backend API)...
taskkill /F /IM uvicorn.exe /T >nul 2>&1

echo.
echo ⏳ Waiting for clean shutdown...
timeout /t 2 /nobreak >nul

echo.
echo 🔍 Final verification...
netstat -an | findstr "8188\|5173" >nul
if errorlevel 1 (
    echo ✅ All ports are now free
    python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "SHUTDOWN" "All services stopped successfully - ports freed"
) else (
    echo ⚠️  Some ports may still be in use
    echo    Check with: netstat -an | findstr "8188\|5173"
    python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_warning "SHUTDOWN" "Some ports may still be in use after shutdown"
)

echo.
echo ================================================================
echo   ✅ All ComfyUI project services stopped.
echo ================================================================
echo.
echo 💡 To restart services, use:
echo    - service_manager.bat (recommended)
echo    - START_COMPLETE_SYSTEM.bat
echo    - quick_start.bat
echo.

python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "SHUTDOWN" "Service shutdown completed"

pause
