from fastapi import APIRouter, HTTPException, UploadFile, File
from fastapi.responses import StreamingResponse
from typing import Dict, List, Any, Optional
import json
import io
import logging
import traceback
from app.utils.frontend_log import post_log_to_frontend, post_generation_progress
from PIL import Image

from app.services.comfyui_service import ComfyUIService
from app.services.ollama_service import OllamaService
from app.services.system_monitor import SystemMonitor
from .prompt_enhancement_routes import router as prompt_enhancement_router
from .semantic_routes import router as semantic_router

# Configure detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

router = APIRouter()

# Include prompt enhancement routes
router.include_router(prompt_enhancement_router)
router.include_router(semantic_router)

# Initialize services
comfyui_service = ComfyUIService()
ollama_service = OllamaService()
system_monitor = SystemMonitor()

# Track active generations for cancellation
active_simple_generations: Dict[str, Dict[str, Any]] = {}

@router.get("/health")
async def health_check():
    """Health check endpoint"""
    comfyui_status = await comfyui_service.check_connection()
    ollama_status = await ollama_service.check_connection()
    
    return {
        "status": "healthy",
        "services": {
            "comfyui": "connected" if comfyui_status else "disconnected",
            "ollama": "connected" if ollama_status else "disconnected"
        }
    }

@router.get("/")
async def api_info():
    """API information endpoint"""
    return {
        "name": "ComfyUI Custom Frontend API",
        "version": "1.0.0",
        "description": "Backend API for ComfyUI Custom Frontend with AI-powered features",
        "endpoints": {
            "health": "/api/v1/health",
            "system": "/api/v1/system/stats",
            "comfyui": "/api/v1/comfyui/status",
            "models": "/api/v1/models"
        }
    }

@router.get("/comfyui/status")
async def get_comfyui_status():
    """Get ComfyUI connection status"""
    is_connected = await comfyui_service.check_connection()
    return {
        "status": "connected" if is_connected else "disconnected",
        "service": "comfyui"
    }

@router.get("/models")
async def get_all_models():
    """Get all available models"""
    try:
        comfyui_models = await comfyui_service.scan_models()
        ollama_models = await ollama_service.list_models()
        
        return {
            "comfyui_models": comfyui_models,
            "ollama_models": ollama_models,
            "total_models": len(comfyui_models.get("models", [])) + len(ollama_models)
        }
    except Exception as e:
        logger.error(f"Error getting models: {e}")
        return {
            "comfyui_models": {"models": []},
            "ollama_models": [],
            "total_models": 0,
            "error": str(e)
        }

@router.get("/system/stats")
async def get_system_stats():
    """Get current system statistics"""
    return await system_monitor.get_current_stats()

@router.get("/system/health")
async def get_system_health():
    """Get system health status"""
    return system_monitor.check_system_health()

# ComfyUI Routes
@router.get("/comfyui/queue")
async def get_comfyui_queue():
    """Get ComfyUI queue status"""
    return await comfyui_service.get_queue_status()

@router.get("/comfyui/models")
async def get_available_models():
    """Get available models with validation"""
    models_data = await comfyui_service.scan_models()
    return models_data

@router.get("/comfyui/models/validated")
async def get_validated_models():
    """Get only validated models that are ready to use"""
    models_data = await comfyui_service.scan_models()
    
    # Return only validated checkpoints with their metadata
    validated_models = []
    for model in models_data.get("validated_checkpoints", []):
        validated_models.append({
            "name": model["name"],
            "architecture": model["architecture"],
            "displayName": f"{model['name']} ({model['architecture'].upper()})"
        })
    
    return {
        "models": validated_models,
        "count": len(validated_models),
        "validation_summary": {
            "total_found": len(models_data.get("checkpoints", [])),
            "validated": len(validated_models),
            "invalid": len(models_data.get("checkpoints", [])) - len(validated_models)
        }
    }

@router.get("/comfyui/models/validation-report")
async def get_model_validation_report():
    """Get detailed validation report for all models"""
    models_data = await comfyui_service.scan_models()
    return {
        "validation_report": models_data.get("validation_report", {}),
        "summary": {
            "total_models": len(models_data.get("checkpoints", [])),
            "valid_models": len(models_data.get("validated_checkpoints", [])),
            "invalid_models": len(models_data.get("checkpoints", [])) - len(models_data.get("validated_checkpoints", []))
        }
    }

@router.get("/comfyui/models/missing-dependencies")
async def get_missing_dependencies():
    """Get information about missing dependencies for invalid models"""
    models_data = await comfyui_service.scan_models()
    validation_report = models_data.get("validation_report", {})
    
    missing_deps = {}
    architecture_counts = {"flux": 0, "sdxl": 0, "sd15": 0, "sd21": 0, "unknown": 0}
    
    for model_name, validation in validation_report.items():
        if not validation.get("valid", True):
            architecture = validation.get("architecture", "unknown")
            architecture_counts[architecture] = architecture_counts.get(architecture, 0) + 1
            
            missing_deps[model_name] = {
                "architecture": architecture,
                "missing_dependencies": validation.get("missing_dependencies", []),
                "warnings": validation.get("warnings", [])
            }
    
    # Generate recommendations
    recommendations = []
    
    # Check for common missing dependencies
    all_missing = []
    for model_data in missing_deps.values():
        all_missing.extend(model_data["missing_dependencies"])
    
    from collections import Counter
    common_missing = Counter(all_missing).most_common(3)
    
    for dep, count in common_missing:
        if "VAE" in dep:
            recommendations.append({
                "type": "download",
                "title": f"Download {dep.split(': ')[1]}",
                "description": f"Required by {count} model(s). Place in L:/ComfyUI/models/vae/",
                "priority": "high" if count > 1 else "medium"
            })
        elif "T5" in dep or "CLIP" in dep:
            recommendations.append({
                "type": "download", 
                "title": f"Download {dep.split(': ')[1]}",
                "description": f"Required by {count} model(s). Place in L:/ComfyUI/models/clip/",
                "priority": "high" if count > 1 else "medium"
            })
    
    return {
        "missing_dependencies": missing_deps,
        "architecture_summary": architecture_counts,
        "recommendations": recommendations,
        "total_invalid_models": len(missing_deps)
    }

@router.post("/comfyui/generate/text-to-image")
async def generate_text_to_image(request: Dict[str, Any]):
    """Generate image from text prompt with enhanced progress tracking"""
    try:
        # Initial setup and validation
        post_generation_progress(0, "initializing", "Received generation request")
        logger.info(f"Received text-to-image request")

        # Debug: Log the received request
        logger.info(f"Request type: {type(request)}")
        logger.info(f"Request content: {request}")

        # Handle different request formats
        if isinstance(request, str):
            try:
                request = json.loads(request)
                logger.info(f"Parsed JSON request: {request}")
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON request: {e}")
                raise HTTPException(status_code=400, detail=f"Invalid JSON: {e}")

        # Extract workflow from request (sent by frontend)
        workflow = request.get("workflow") if hasattr(request, 'get') else None
        if not workflow:
            post_log_to_frontend("❌ No workflow provided in request", "error")
            raise HTTPException(status_code=400, detail="Workflow is required")

        # Validate workflow is a dictionary
        if not isinstance(workflow, dict):
            logger.error(f"Workflow is not a dictionary: {type(workflow)} - {workflow}")
            post_log_to_frontend("❌ Invalid workflow format", "error")
            raise HTTPException(status_code=400, detail=f"Workflow must be a dictionary, got {type(workflow)}")

        post_generation_progress(10, "processing", "Validating workflow parameters")
        post_log_to_frontend("🔍 Validating workflow parameters...")

        # Extract basic info for logging with safe access
        nodes = workflow.get("nodes", [])
        if not isinstance(nodes, list):
            logger.error(f"Workflow nodes is not a list: {type(nodes)} - {nodes}")
            raise HTTPException(status_code=400, detail=f"Workflow nodes must be a list, got {type(nodes)}")

        prompt_node = next((node for node in nodes if isinstance(node, dict) and node.get("type") == "Prompt"), None)
        prompt_text = prompt_node.get("value", "Unknown") if prompt_node else "Unknown"

        post_generation_progress(20, "building", "Building ComfyUI workflow")
        post_log_to_frontend(f"🎨 Building workflow for prompt: {prompt_text[:50]}...")

        # For now, use the existing workflow building logic
        # TODO: Implement proper frontend workflow conversion
        prompt = prompt_text if prompt_text != "Unknown" else ""
        if not prompt:
            post_log_to_frontend("❌ No prompt found in workflow", "error")
            raise HTTPException(status_code=400, detail="Prompt is required")

        # Extract other parameters from workflow nodes
        model_node = next((node for node in nodes if isinstance(node, dict) and node.get("type") == "Model"), None)
        sampler_node = next((node for node in nodes if isinstance(node, dict) and node.get("type") == "Sampler"), None)
        dimensions_node = next((node for node in nodes if isinstance(node, dict) and node.get("type") == "Dimensions"), None)
        steps_node = next((node for node in nodes if isinstance(node, dict) and node.get("type") == "Steps"), None)
        cfg_node = next((node for node in nodes if isinstance(node, dict) and node.get("type") == "CFG"), None)
        seed_node = next((node for node in nodes if isinstance(node, dict) and node.get("type") == "Seed"), None)
        batch_node = next((node for node in nodes if isinstance(node, dict) and node.get("type") == "BatchSize"), None)

        # Extract parameters from nodes with defaults
        model = model_node.get("value", "flux1-kontext-dev.safetensors") if model_node else "flux1-kontext-dev.safetensors"
        sampler = sampler_node.get("value", "euler") if sampler_node else "euler"

        # Extract dimensions
        width = dimensions_node.get("width", 1024) if dimensions_node else 1024
        height = dimensions_node.get("height", 1024) if dimensions_node else 1024

        # Extract other parameters
        steps = steps_node.get("value", 20) if steps_node else 20
        cfg = cfg_node.get("value", 7.0) if cfg_node else 7.0
        seed = seed_node.get("value", -1) if seed_node else -1
        batch_size = batch_node.get("value", 1) if batch_node else 1

        logger.info(f"Extracted parameters: model={model}, prompt={prompt_text[:50]}, dimensions={width}x{height}, steps={steps}, cfg={cfg}")
        post_log_to_frontend(f"📋 Parameters: {model}, {width}x{height}, {steps} steps, CFG {cfg}")

        post_generation_progress(30, "building", "Converting to ComfyUI format")

        # Build workflow using existing service
        comfyui_workflow = comfyui_service.build_text_to_image_workflow(
            prompt=prompt,
            negative_prompt="",  # Extract from workflow if needed
            model=model,
            width=width,
            height=height,
            steps=steps,
            cfg=cfg,
            seed=seed
        )

        post_generation_progress(40, "submitting", "Submitting to ComfyUI")
        post_log_to_frontend("📤 Submitting workflow to ComfyUI...")

        # Submit to ComfyUI
        prompt_id = await comfyui_service.submit_workflow(comfyui_workflow)

        post_generation_progress(50, "generating", "Generation in progress")
        post_log_to_frontend(f"✅ Workflow submitted with ID: {prompt_id}")
        post_log_to_frontend("⏳ Generation in progress...")

        # Wait for completion and get the image URL
        import asyncio
        max_wait_time = 300  # 5 minutes timeout
        poll_interval = 2    # Check every 2 seconds
        elapsed_time = 0
        
        while elapsed_time < max_wait_time:
            await asyncio.sleep(poll_interval)
            elapsed_time += poll_interval
            
            # Update progress
            progress = 50 + (elapsed_time / max_wait_time) * 40  # 50% to 90%
            post_generation_progress(progress, "generating", f"Generation in progress... ({elapsed_time}s)")
            
            # Check status
            status_data = await comfyui_service.get_workflow_status(prompt_id)
            status = status_data.get("status", "unknown")
            
            if status == "completed":
                post_generation_progress(95, "completed", "Getting generated image...")
                post_log_to_frontend("🎉 Generation completed! Retrieving image...")
                
                # Get the generated images
                images = await comfyui_service.get_generated_images(prompt_id)
                
                if images:
                    # Use the first image
                    image_info = images[0]
                    filename = image_info["filename"]
                    subfolder = image_info.get("subfolder", "")
                    image_type = image_info.get("type", "output")
                    
                    # Build the image URL that the frontend can access
                    image_url = f"/api/v1/comfyui/image/{filename}"
                    if subfolder:
                        image_url += f"?subfolder={subfolder}&type={image_type}"
                    else:
                        image_url += f"?type={image_type}"
                    
                    post_generation_progress(100, "completed", "Image ready!")
                    post_log_to_frontend(f"✅ Image generated successfully: {filename}")
                    
                    return {
                        "success": True,
                        "prompt_id": prompt_id,
                        "status": "completed",
                        "image_url": image_url,
                        "filename": filename,
                        "message": "Generation completed successfully"
                    }
                else:
                    post_log_to_frontend("⚠️ No images found in completed generation")
                    break
                    
            elif status == "failed":
                post_generation_progress(0, "failed", "Generation failed")
                post_log_to_frontend("❌ Generation failed")
                error_msg = status_data.get("error", "Unknown error")
                raise HTTPException(status_code=500, detail={
                    "error": f"Generation failed: {error_msg}",
                    "prompt_id": prompt_id,
                    "success": False
                })
        
        # Timeout reached
        post_generation_progress(0, "timeout", "Generation timed out")
        post_log_to_frontend(f"⏰ Generation timed out after {max_wait_time} seconds")
        return {
            "success": False,
            "prompt_id": prompt_id,
            "status": "timeout",
            "error": f"Generation timed out after {max_wait_time} seconds",
            "message": "You can check the status later using the prompt_id"
        }

    except Exception as e:
        post_generation_progress(0, "failed", f"Error: {str(e)}")
        logger.error(f"Text-to-image generation failed: {str(e)}")
        post_log_to_frontend(f"❌ Generation error: {str(e)}", "error")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": str(e),
                "type": type(e).__name__,
                "success": False
            }
        )

@router.get("/comfyui/status/{prompt_id}")
async def get_generation_status(prompt_id: str):
    """Get generation status with image data"""
    try:
        status_data = await comfyui_service.get_workflow_status(prompt_id)
        images = await comfyui_service.get_generated_images(prompt_id)

        # Extract the actual status string from the status object
        status_string = status_data.get("status", "unknown") if isinstance(status_data, dict) else "unknown"

        response = {
            "prompt_id": prompt_id,
            "status": status_string,
            "status_data": status_data,  # Include full status data for debugging
            "images": images
        }

        # If generation is completed and we have images, include base64 image data
        if status_string == "completed" and images:
            try:
                import base64
                # Get the first image and convert to base64
                image_info = images[0]
                filename = image_info["filename"]
                subfolder = image_info.get("subfolder", "")
                type_param = image_info.get("type", "output")
                
                logger.info(f"Converting completed image to base64: {filename}")
                
                # Download the image data
                image_bytes = await comfyui_service.download_image(filename, subfolder, type_param)
                
                if image_bytes:
                    # Convert to base64
                    image_data = base64.b64encode(image_bytes).decode('utf-8')
                    
                    # Determine image type from filename
                    file_ext = filename.lower().split('.')[-1]
                    if file_ext in ['jpg', 'jpeg']:
                        mime_type = 'image/jpeg'
                    elif file_ext == 'png':
                        mime_type = 'image/png'
                    elif file_ext == 'webp':
                        mime_type = 'image/webp'
                    else:
                        mime_type = 'image/png'  # Default to PNG
                    
                    # Add image data to response
                    response["imageData"] = f"data:{mime_type};base64,{image_data}"
                    logger.info(f"Successfully added base64 image data to status response, size: {len(image_data)}")
                else:
                    logger.warning(f"No image bytes received for {filename}")
                    
            except Exception as img_error:
                logger.error(f"Failed to convert image to base64: {img_error}")
                # Don't fail the whole response if image conversion fails
                response["imageError"] = str(img_error)

        return response
    except Exception as e:
        logger.error(f"Error getting generation status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/comfyui/image/{filename}")
async def get_generated_image(filename: str, subfolder: str = "", type: str = "output"):
    """Serve generated image for display"""
    try:
        logger.info(f"Serving image: {filename}, subfolder: {subfolder}, type: {type}")
        image_data = await comfyui_service.download_image(filename, subfolder, type)
        
        # Determine correct MIME type based on file extension
        mime_type = "image/png"
        if filename.lower().endswith(('.jpg', '.jpeg')):
            mime_type = "image/jpeg"
        elif filename.lower().endswith('.webp'):
            mime_type = "image/webp"
        elif filename.lower().endswith('.gif'):
            mime_type = "image/gif"
        
        return StreamingResponse(
            io.BytesIO(image_data),
            media_type=mime_type,
            headers={
                "Cache-Control": "public, max-age=3600",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET",
                "Access-Control-Allow-Headers": "*"
            }
        )
    except Exception as e:
        logger.error(f"Error serving image {filename}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Ollama Routes
@router.get("/ollama/models")
async def get_ollama_models():
    """Get available Ollama models"""
    try:
        models = await ollama_service.list_models()
        return {"models": models}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/ollama/generate")
async def generate_ollama_response(request: Dict[str, Any]):
    """Generate response using Ollama"""
    try:
        prompt = request.get("prompt", "")
        model = request.get("model")
        system_prompt = request.get("system_prompt", "")
        temperature = request.get("temperature", 0.7)
        max_tokens = request.get("max_tokens", 2000)
        
        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt is required")
        
        response = await ollama_service.generate_response(
            prompt=prompt,
            model=model,
            system_prompt=system_prompt,
            temperature=temperature,
            max_tokens=max_tokens
        )
        
        return {"response": response}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/ollama/creative-prompt")
async def generate_creative_prompt(request: Dict[str, Any]):
    """Generate creative prompt using AI"""
    try:
        base_theme = request.get("base_theme", "")
        style = request.get("style", "photorealistic")
        mood = request.get("mood", "dramatic")
        lighting = request.get("lighting", "cinematic")
        
        result = await ollama_service.generate_creative_prompt(
            base_theme=base_theme,
            style=style,
            mood=mood,
            lighting=lighting
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/ollama/enhance-prompt")
async def enhance_prompt(request: Dict[str, Any]):
    """Enhance a basic prompt with model-specific optimization"""
    try:
        logger.info(f"Received prompt enhancement request")
        post_log_to_frontend("🤖 Received prompt enhancement request", "info")

        base_prompt = request.get("prompt", "")
        model_name = request.get("model", "flux")  # Default to flux
        style_preferences = request.get("style_preferences", [])

        if not base_prompt:
            logger.error("No prompt provided for enhancement")
            post_log_to_frontend("❌ No prompt provided for enhancement", "error")
            raise HTTPException(status_code=400, detail="Prompt is required")

        logger.info(f"Enhancing prompt for model: {model_name}")
        post_log_to_frontend(f"🎨 Enhancing prompt for {model_name} model...", "info")

        # Extract model type from filename for better detection
        if isinstance(model_name, str) and ('/' in model_name or '\\' in model_name):
            model_file = model_name.split('/')[-1].split('\\')[-1].lower()
        else:
            model_file = str(model_name).lower()

        enhanced = await ollama_service.enhance_prompt(
            base_prompt=base_prompt,
            model_name=model_file,
            style_preferences=style_preferences
        )

        logger.info(f"Prompt enhanced successfully")
        post_log_to_frontend("✨ Prompt enhanced successfully!", "success")

        return {
            "enhanced_prompt": enhanced,
            "original_prompt": base_prompt,
            "model_used": model_file,
            "enhancement_model": "llama3.2:latest"
        }

    except Exception as e:
        logger.error(f"Prompt enhancement failed: {str(e)}")
        post_log_to_frontend(f"❌ Prompt enhancement failed: {str(e)}", "error")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": str(e),
                "type": type(e).__name__,
                "traceback": traceback.format_exc()
            }
        )

# Image Enhancement Routes
@router.post("/enhance/upscale")
async def upscale_image(
    file: UploadFile = File(...),
    model: str = "RealESRGAN_4x",
    scale: int = 4
):
    """Upscale an image using ComfyUI"""
    try:
        # Read uploaded image
        image_data = await file.read()
        
        # TODO: Implement image upscaling workflow
        # This would involve:
        # 1. Save uploaded image to temp location
        # 2. Build upscaling workflow
        # 3. Submit to ComfyUI
        # 4. Return prompt_id for status tracking
        
        return {
            "message": "Image upscaling not yet implemented",
            "filename": file.filename,
            "model": model,
            "scale": scale
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Utility Routes
@router.post("/utils/analyze-image")
async def analyze_image(file: UploadFile = File(...)):
    """Analyze uploaded image for reference mode"""
    try:
        image_data = await file.read()
        
        # Open image with PIL
        image = Image.open(io.BytesIO(image_data))
        
        # Basic image analysis
        analysis = {
            "filename": file.filename,
            "format": image.format,
            "mode": image.mode,
            "size": image.size,
            "width": image.width,
            "height": image.height,
            "aspect_ratio": round(image.width / image.height, 2),
            "file_size_bytes": len(image_data),
            "estimated_colors": len(colors) if image.mode == "RGB" and (colors := image.getcolors(maxcolors=256*256*256)) else "N/A"
        }
        
        # TODO: Add more sophisticated analysis
        # - Color palette extraction
        # - Style detection
        # - Composition analysis
        
        return analysis
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# File Explorer Routes
@router.get("/explorer/folder")
async def get_folder_contents(path: str = "/"):
    """Get contents of a folder for the file explorer"""
    import os
    import pathlib
    from datetime import datetime
    
    try:
        # Define safe base directories
        base_dirs = {
            "/": "L:/ComfyUI/output",
            "output": "L:/ComfyUI/output", 
            "input": "L:/ComfyUI/input",
            "temp": "L:/ComfyUI/temp",
            "models": "L:/ComfyUI/models"
        }
        
        # Resolve the actual path
        if path in base_dirs:
            actual_path = base_dirs[path]
        elif path.startswith("output/"):
            actual_path = os.path.join("L:/ComfyUI/output", path[7:])
        elif path.startswith("input/"):
            actual_path = os.path.join("L:/ComfyUI/input", path[6:])
        elif path.startswith("temp/"):
            actual_path = os.path.join("L:/ComfyUI/temp", path[5:])
        elif path.startswith("models/"):
            actual_path = os.path.join("L:/ComfyUI/models", path[7:])
        else:
            actual_path = "L:/ComfyUI/output"  # Default fallback
        
        # Security check: ensure path is within allowed directories
        actual_path = os.path.abspath(actual_path)
        allowed_base = os.path.abspath("L:/ComfyUI")
        if not actual_path.startswith(allowed_base):
            raise HTTPException(status_code=403, detail="Access denied to path outside ComfyUI directory")
        
        # Check if directory exists
        if not os.path.exists(actual_path):
            raise HTTPException(status_code=404, detail=f"Directory not found: {path}")
        
        if not os.path.isdir(actual_path):
            raise HTTPException(status_code=400, detail=f"Path is not a directory: {path}")
        
        items = []
        
        try:
            for item_name in sorted(os.listdir(actual_path)):
                item_path = os.path.join(actual_path, item_name)
                
                # Skip hidden files and system files
                if item_name.startswith('.') or item_name.startswith('__'):
                    continue
                
                # Get file info
                stat_info = os.stat(item_path)
                modified = datetime.fromtimestamp(stat_info.st_mtime)
                
                is_dir = os.path.isdir(item_path)
                is_image = False
                thumbnail = None
                
                if not is_dir:
                    # Check if it's an image file
                    image_extensions = {'.png', '.jpg', '.jpeg', '.webp', '.gif', '.bmp', '.tiff'}
                    ext = pathlib.Path(item_name).suffix.lower()
                    is_image = ext in image_extensions
                    
                    # For images, we could generate thumbnails here
                    # For now, we'll use the full image path as thumbnail
                    if is_image:
                        # Convert absolute path to relative URL for frontend
                        thumbnail = f"/api/v1/comfyui/image/{item_name}?subfolder={path}&type=output"
                
                items.append({
                    "id": f"{path}/{item_name}",
                    "name": item_name,
                    "type": "folder" if is_dir else "image" if is_image else "file",
                    "path": os.path.join(path, item_name).replace("\\", "/"),
                    "size": stat_info.st_size if not is_dir else None,
                    "modified": modified.isoformat(),
                    "thumbnail": thumbnail
                })
                
        except PermissionError:
            raise HTTPException(status_code=403, detail=f"Permission denied accessing directory: {path}")
        
        return items
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reading directory {path}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error reading directory: {str(e)}")

@router.post("/explorer/create-folder")
async def create_folder(request: Dict[str, Any]):
    """Create a new folder"""
    import os
    
    try:
        path = request.get("path", "")
        name = request.get("name", "")
        
        if not path or not name:
            raise HTTPException(status_code=400, detail="Path and name are required")
        
        # TODO: Implement folder creation with security checks
        return {"message": "Folder creation not yet implemented"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/explorer/delete")
async def delete_items(request: Dict[str, Any]):
    """Delete files or folders"""
    try:
        items = request.get("items", [])
        
        if not items:
            raise HTTPException(status_code=400, detail="No items specified for deletion")
        
        # TODO: Implement secure file deletion
        return {"message": "File deletion not yet implemented"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/comfyui/generate/text-to-image")
async def generate_text_to_image(request: Dict[str, Any]):
    """
    Generate text-to-image using ComfyUI workflow
    This endpoint matches what the frontend expects
    """
    try:
        logger.info("🎨 Text-to-image generation request received")
        
        # Extract workflow and generation ID from request
        workflow = request.get("workflow")
        generation_id = request.get("generation_id")
        
        if not workflow:
            raise HTTPException(status_code=400, detail="Workflow is required")
        
        logger.info(f"📋 Workflow nodes: {len(workflow.get('nodes', []))}")
        logger.info(f"🆔 Generation ID: {generation_id}")
        
        # Submit workflow to ComfyUI
        prompt_id = await comfyui_service.submit_workflow(workflow)
        if not prompt_id:
            raise HTTPException(status_code=500, detail="Failed to submit workflow to ComfyUI")
        
        logger.info(f"✅ Workflow submitted with prompt_id: {prompt_id}")
        
        # Store generation info for cancellation tracking
        if generation_id:
            active_simple_generations[generation_id] = {
                "prompt_id": prompt_id,
                "status": "processing",
                "created_at": logger.info.__module__  # Just a timestamp placeholder
            }
            logger.info(f"💾 Stored generation tracking for {generation_id} -> {prompt_id}")
        
        # Wait for completion (with timeout)
        import asyncio
        timeout_seconds = 300  # 5 minutes
        start_time = asyncio.get_event_loop().time()
        
        while True:
            current_time = asyncio.get_event_loop().time()
            if current_time - start_time > timeout_seconds:
                logger.error(f"⏰ Generation timed out after {timeout_seconds} seconds")
                # Clean up tracking
                if generation_id and generation_id in active_simple_generations:
                    del active_simple_generations[generation_id]
                raise HTTPException(status_code=504, detail="Generation timed out")
            
            # Check if generation was cancelled
            if generation_id and generation_id in active_simple_generations:
                if active_simple_generations[generation_id].get("status") == "cancelled":
                    logger.info(f"🛑 Generation {generation_id} was cancelled")
                    del active_simple_generations[generation_id]
                    raise HTTPException(status_code=499, detail="Generation was cancelled")
            
            # Check status
            status_data = await comfyui_service.get_workflow_status(prompt_id)
            status = status_data.get("status")
            
            logger.info(f"📊 Current status: {status}")
            
            if status == "completed":
                logger.info("🎉 Generation completed successfully")
                
                # Clean up tracking
                if generation_id and generation_id in active_simple_generations:
                    del active_simple_generations[generation_id]
                
                # Get generated images
                images = await comfyui_service.get_generated_images(prompt_id)
                if images and len(images) > 0:
                    image_info = images[0]
                    filename = image_info.get("filename")
                    subfolder = image_info.get("subfolder", "")
                    
                    logger.info(f"🖼️ Generated image: {filename}")
                    
                    # Create image URL - this should be accessible via ComfyUI's static file serving
                    # ComfyUI serves images at /view?filename=...&subfolder=...&type=output
                    image_url = f"/api/v1/comfyui/view?filename={filename}"
                    if subfolder:
                        image_url += f"&subfolder={subfolder}"
                    image_url += "&type=output"
                    
                    logger.info(f"🔗 Image URL: {image_url}")
                    
                    return {
                        "success": True,
                        "image_url": image_url,
                        "prompt_id": prompt_id,
                        "filename": filename,
                        "message": "Image generated successfully"
                    }
                else:
                    logger.warning("⚠️ No images found in result")
                    raise HTTPException(status_code=500, detail="No images generated")
                    
            elif status == "failed":
                error_msg = status_data.get("error", "Generation failed")
                logger.error(f"❌ Generation failed: {error_msg}")
                # Clean up tracking
                if generation_id and generation_id in active_simple_generations:
                    del active_simple_generations[generation_id]
                raise HTTPException(status_code=500, detail=f"Generation failed: {error_msg}")
            
            # Continue waiting
            await asyncio.sleep(2)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"💥 Unexpected error in text-to-image generation: {str(e)}")
        logger.error(traceback.format_exc())
        # Clean up tracking on error
        generation_id = request.get("generation_id")
        if generation_id and generation_id in active_simple_generations:
            del active_simple_generations[generation_id]
        raise HTTPException(status_code=500, detail=f"Generation error: {str(e)}")

@router.post("/comfyui/cancel/{generation_id}")
async def cancel_simple_generation(generation_id: str):
    """Cancel a simple text-to-image generation"""
    try:
        logger.info(f"🛑 Cancel request received for generation_id: {generation_id}")
        
        # Check if generation exists in tracking
        if generation_id in active_simple_generations:
            generation_data = active_simple_generations[generation_id]
            prompt_id = generation_data.get("prompt_id")
            
            # Mark as cancelled first
            active_simple_generations[generation_id]["status"] = "cancelled"
            
            # Cancel in ComfyUI if we have a prompt_id
            if prompt_id:
                try:
                    await comfyui_service.cancel_generation(prompt_id)
                    logger.info(f"✅ ComfyUI cancellation requested for prompt_id: {prompt_id}")
                except Exception as e:
                    logger.error(f"⚠️ ComfyUI cancellation failed: {e}")
            
            # Remove from tracking after marking as cancelled
            # (The generation loop will detect this and stop)
            logger.info(f"🗑️ Generation {generation_id} marked for cancellation")
        else:
            logger.warning(f"⚠️ Generation {generation_id} not found in active tracking")
        
        # Always try to cleanup ComfyUI queue/memory regardless
        try:
            await comfyui_service.clear_queue()
            await comfyui_service.free_memory()
            logger.info("🧹 ComfyUI queue cleared and memory freed")
        except Exception as e:
            logger.error(f"⚠️ ComfyUI cleanup failed: {e}")
        
        return {
            "success": True,
            "message": f"Generation {generation_id} cancelled successfully",
            "generation_id": generation_id
        }
        
    except Exception as e:
        logger.error(f"💥 Cancel generation failed: {e}")
        # Still try to cleanup ComfyUI
        try:
            await comfyui_service.clear_queue()
            await comfyui_service.free_memory()
        except:
            pass
            
        return {
            "success": True,  # Return success even on errors to ensure frontend cleanup
            "message": f"Generation cancelled (forced cleanup)",
            "generation_id": generation_id,
            "warning": str(e)
        }


