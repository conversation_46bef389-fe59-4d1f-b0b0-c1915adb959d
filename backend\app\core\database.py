"""
Database configuration and connection management
"""

import os
import asyncio
from typing import As<PERSON><PERSON><PERSON>ator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool
from sqlalchemy import event, text
from app.models.workflow_execution import Base
import logging

logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite+aiosqlite:///./data/workflow_tracking.db')

# Create async engine
engine = create_async_engine(
    DATABASE_URL,
    echo=False,  # Set to True for SQL debugging
    poolclass=StaticPool,
    connect_args={
        "check_same_thread": False,
        "timeout": 30
    } if 'sqlite' in DATABASE_URL else {},
    pool_pre_ping=True,
    pool_recycle=300  # 5 minutes
)

# Create session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)


# SQLite-specific optimizations
@event.listens_for(engine.sync_engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """Set SQLite pragmas for better performance"""
    if 'sqlite' in DATABASE_URL:
        cursor = dbapi_connection.cursor()
        # Enable WAL mode for better concurrent access
        cursor.execute("PRAGMA journal_mode=WAL")
        # Set synchronous mode to NORMAL for better performance
        cursor.execute("PRAGMA synchronous=NORMAL")
        # Enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys=ON")
        # Set cache size (negative value = KB)
        cursor.execute("PRAGMA cache_size=-64000")  # 64MB cache
        # Set temp store to memory
        cursor.execute("PRAGMA temp_store=MEMORY")
        # Set mmap size for better I/O
        cursor.execute("PRAGMA mmap_size=268435456")  # 256MB
        cursor.close()


async def init_database():
    """Initialize database tables"""
    try:
        # Ensure data directory exists
        os.makedirs('data', exist_ok=True)
        
        async with engine.begin() as conn:
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created/verified successfully")
            
        # Test connection
        async with AsyncSessionLocal() as session:
            result = await session.execute(text("SELECT 1"))
            if result.scalar() == 1:
                logger.info("Database connection test successful")
            else:
                raise Exception("Database connection test failed")
                
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session for dependency injection"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            await session.close()


async def health_check() -> dict:
    """Check database health"""
    try:
        async with AsyncSessionLocal() as session:
            # Test query
            result = await session.execute(text("SELECT 1"))
            if result.scalar() != 1:
                return {"status": "unhealthy", "error": "Query test failed"}
            
            # Check table existence
            result = await session.execute(
                text("SELECT name FROM sqlite_master WHERE type='table' AND name='workflow_executions'")
            )
            if not result.scalar():
                return {"status": "unhealthy", "error": "Required tables missing"}
            
            return {
                "status": "healthy",
                "database_url": DATABASE_URL.split('/')[-1],  # Just filename for security
                "tables_exist": True
            }
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}


async def cleanup_old_records(days_to_keep: int = 30):
    """Clean up old workflow execution records"""
    try:
        from datetime import datetime, timedelta
        from app.models.workflow_execution import WorkflowExecution
        
        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
        
        async with AsyncSessionLocal() as session:
            # Count records to be deleted
            from sqlalchemy import select, func
            count_query = select(func.count(WorkflowExecution.id)).where(
                WorkflowExecution.timestamp < cutoff_date
            )
            result = await session.execute(count_query)
            count = result.scalar()
            
            if count > 0:
                # Delete old records (cascading will handle related records)
                from sqlalchemy import delete
                delete_query = delete(WorkflowExecution).where(
                    WorkflowExecution.timestamp < cutoff_date
                )
                await session.execute(delete_query)
                await session.commit()
                
                logger.info(f"Cleaned up {count} old workflow execution records")
                return count
            else:
                logger.info("No old records to clean up")
                return 0
                
    except Exception as e:
        logger.error(f"Failed to cleanup old records: {e}")
        raise


async def get_database_stats() -> dict:
    """Get database statistics"""
    try:
        async with AsyncSessionLocal() as session:
            from sqlalchemy import select, func, text
            from app.models.workflow_execution import WorkflowExecution, WorkflowCorrection
            
            # Get table counts
            execution_count = await session.execute(
                select(func.count(WorkflowExecution.id))
            )
            correction_count = await session.execute(
                select(func.count(WorkflowCorrection.id))
            )
            
            # Get database size (SQLite specific)
            if 'sqlite' in DATABASE_URL:
                size_result = await session.execute(
                    text("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
                )
                db_size = size_result.scalar()
            else:
                db_size = None
            
            # Get recent activity
            recent_executions = await session.execute(
                select(func.count(WorkflowExecution.id)).where(
                    WorkflowExecution.timestamp >= func.date('now', '-7 days')
                )
            )
            
            return {
                "total_executions": execution_count.scalar(),
                "total_corrections": correction_count.scalar(),
                "recent_executions_7d": recent_executions.scalar(),
                "database_size_bytes": db_size,
                "database_size_mb": round(db_size / 1024 / 1024, 2) if db_size else None
            }
            
    except Exception as e:
        logger.error(f"Failed to get database stats: {e}")
        return {"error": str(e)}


# Export session for use in services
get_db = get_db_session