"""
SQLAlchemy models for workflow execution tracking
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, Session
from sqlalchemy.dialects.sqlite import J<PERSON><PERSON> as SQLiteJSON
import json

Base = declarative_base()


class WorkflowExecution(Base):
    """
    Main workflow execution record
    """
    __tablename__ = "workflow_executions"

    id = Column(String, primary_key=True)  # workflow_TIMESTAMP_RANDOM
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    user_id = Column(String, nullable=True)
    
    # Request data
    model_name = Column(String, nullable=False)
    model_type = Column(String, nullable=True)
    generation_mode = Column(String, nullable=False)  # txt2img, img2img, etc.
    
    # Generation settings (JSON)
    generation_settings = Column(SQLiteJSON, nullable=False)
    original_request = Column(SQLiteJSON, nullable=False)
    corrected_request = Column(SQLiteJSON, nullable=True)
    
    # Workflow data
    original_workflow = Column(Text, nullable=False)  # JSON string
    final_workflow = Column(Text, nullable=True)  # JSON string after corrections
    node_count = Column(Integer, nullable=False)
    
    # Validation results
    auto_corrections_applied = Column(Integer, default=0)
    validation_warnings = Column(Integer, default=0)
    validation_errors = Column(Integer, default=0)
    
    # Execution status
    execution_status = Column(String, nullable=False, default='submitted')  # submitted, processing, completed, failed, cancelled
    execution_start_time = Column(DateTime, nullable=True)
    execution_end_time = Column(DateTime, nullable=True)
    execution_duration = Column(Float, nullable=True)  # milliseconds
    
    # Results
    success = Column(Boolean, default=False)
    error_message = Column(Text, nullable=True)
    error_details = Column(SQLiteJSON, nullable=True)
    image_generated = Column(Boolean, default=False)
    image_size = Column(Integer, nullable=True)  # bytes
    
    # Performance data
    queue_time = Column(Float, nullable=True)
    processing_time = Column(Float, nullable=True)
    memory_usage = Column(Float, nullable=True)
    gpu_utilization = Column(Float, nullable=True)
    
    # System information
    comfyui_version = Column(String, nullable=True)
    frontend_version = Column(String, nullable=False, default='1.0.0')
    platform = Column(String, nullable=True)
    user_agent = Column(String, nullable=True)
    screen_resolution = Column(String, nullable=True)
    
    # Metadata
    tags = Column(Text, nullable=True)  # Comma-separated tags
    notes = Column(Text, nullable=True)
    
    # Relationships
    corrections = relationship("WorkflowCorrection", back_populates="execution", cascade="all, delete-orphan")
    warnings = relationship("WorkflowWarning", back_populates="execution", cascade="all, delete-orphan")
    errors = relationship("WorkflowError", back_populates="execution", cascade="all, delete-orphan")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'user_id': self.user_id,
            'model_name': self.model_name,
            'model_type': self.model_type,
            'generation_mode': self.generation_mode,
            'generation_settings': self.generation_settings,
            'original_request': self.original_request,
            'corrected_request': self.corrected_request,
            'node_count': self.node_count,
            'auto_corrections_applied': self.auto_corrections_applied,
            'validation_warnings': self.validation_warnings,
            'validation_errors': self.validation_errors,
            'execution_status': self.execution_status,
            'execution_start_time': self.execution_start_time.isoformat() if self.execution_start_time else None,
            'execution_end_time': self.execution_end_time.isoformat() if self.execution_end_time else None,
            'execution_duration': self.execution_duration,
            'success': self.success,
            'error_message': self.error_message,
            'error_details': self.error_details,
            'image_generated': self.image_generated,
            'image_size': self.image_size,
            'queue_time': self.queue_time,
            'processing_time': self.processing_time,
            'memory_usage': self.memory_usage,
            'gpu_utilization': self.gpu_utilization,
            'comfyui_version': self.comfyui_version,
            'frontend_version': self.frontend_version,
            'platform': self.platform,
            'user_agent': self.user_agent,
            'screen_resolution': self.screen_resolution,
            'tags': self.tags.split(',') if self.tags else [],
            'notes': self.notes
        }

    @property
    def tags_list(self) -> List[str]:
        """Get tags as a list"""
        return self.tags.split(',') if self.tags else []

    @tags_list.setter
    def tags_list(self, tags: List[str]):
        """Set tags from a list"""
        self.tags = ','.join(tags) if tags else None


class WorkflowCorrection(Base):
    """
    Auto-corrections applied to workflow parameters
    """
    __tablename__ = "workflow_corrections"

    id = Column(Integer, primary_key=True, autoincrement=True)
    execution_id = Column(String, ForeignKey('workflow_executions.id'), nullable=False)
    
    node_id = Column(String, nullable=False)
    node_class = Column(String, nullable=False)
    parameter = Column(String, nullable=False)
    original_value = Column(Text, nullable=False)
    corrected_value = Column(Text, nullable=False)
    reason = Column(Text, nullable=False)
    severity = Column(String, nullable=False)  # minor, moderate, critical
    
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # Relationship
    execution = relationship("WorkflowExecution", back_populates="corrections")

    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'execution_id': self.execution_id,
            'node_id': self.node_id,
            'node_class': self.node_class,
            'parameter': self.parameter,
            'original_value': self.original_value,
            'corrected_value': self.corrected_value,
            'reason': self.reason,
            'severity': self.severity,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }


class WorkflowWarning(Base):
    """
    Validation warnings for workflow
    """
    __tablename__ = "workflow_warnings"

    id = Column(Integer, primary_key=True, autoincrement=True)
    execution_id = Column(String, ForeignKey('workflow_executions.id'), nullable=False)
    
    node_id = Column(String, nullable=False)
    node_class = Column(String, nullable=False)
    parameter = Column(String, nullable=True)
    message = Column(Text, nullable=False)
    suggestion = Column(Text, nullable=True)
    
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # Relationship
    execution = relationship("WorkflowExecution", back_populates="warnings")

    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'execution_id': self.execution_id,
            'node_id': self.node_id,
            'node_class': self.node_class,
            'parameter': self.parameter,
            'message': self.message,
            'suggestion': self.suggestion,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }


class WorkflowError(Base):
    """
    Validation errors for workflow
    """
    __tablename__ = "workflow_errors"

    id = Column(Integer, primary_key=True, autoincrement=True)
    execution_id = Column(String, ForeignKey('workflow_executions.id'), nullable=False)
    
    node_id = Column(String, nullable=False)
    node_class = Column(String, nullable=False)
    parameter = Column(String, nullable=True)
    message = Column(Text, nullable=False)
    fatal = Column(Boolean, default=False)
    
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # Relationship
    execution = relationship("WorkflowExecution", back_populates="errors")

    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'execution_id': self.execution_id,
            'node_id': self.node_id,
            'node_class': self.node_class,
            'parameter': self.parameter,
            'message': self.message,
            'fatal': self.fatal,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }


class ModelUsageStats(Base):
    """
    Model usage statistics for analytics
    """
    __tablename__ = "model_usage_stats"

    id = Column(Integer, primary_key=True, autoincrement=True)
    model_name = Column(String, nullable=False, unique=True)
    model_type = Column(String, nullable=True)
    
    total_uses = Column(Integer, default=0)
    successful_uses = Column(Integer, default=0)
    failed_uses = Column(Integer, default=0)
    
    total_execution_time = Column(Float, default=0.0)  # milliseconds
    average_execution_time = Column(Float, default=0.0)
    
    last_used = Column(DateTime, nullable=True)
    first_used = Column(DateTime, default=datetime.utcnow)
    
    # Performance metrics
    average_memory_usage = Column(Float, nullable=True)
    average_gpu_utilization = Column(Float, nullable=True)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        success_rate = (self.successful_uses / self.total_uses * 100) if self.total_uses > 0 else 0
        
        return {
            'id': self.id,
            'model_name': self.model_name,
            'model_type': self.model_type,
            'total_uses': self.total_uses,
            'successful_uses': self.successful_uses,
            'failed_uses': self.failed_uses,
            'success_rate': round(success_rate, 2),
            'total_execution_time': self.total_execution_time,
            'average_execution_time': self.average_execution_time,
            'last_used': self.last_used.isoformat() if self.last_used else None,
            'first_used': self.first_used.isoformat() if self.first_used else None,
            'average_memory_usage': self.average_memory_usage,
            'average_gpu_utilization': self.average_gpu_utilization,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class SystemStats(Base):
    """
    System performance statistics
    """
    __tablename__ = "system_stats"

    id = Column(Integer, primary_key=True, autoincrement=True)
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Performance metrics
    cpu_usage = Column(Float, nullable=True)
    memory_usage = Column(Float, nullable=True)
    gpu_usage = Column(Float, nullable=True)
    gpu_memory_used = Column(Float, nullable=True)
    gpu_memory_total = Column(Float, nullable=True)
    
    # Active generations
    active_generations = Column(Integer, default=0)
    queue_length = Column(Integer, default=0)
    
    # System info
    comfyui_version = Column(String, nullable=True)
    frontend_version = Column(String, nullable=True)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'cpu_usage': self.cpu_usage,
            'memory_usage': self.memory_usage,
            'gpu_usage': self.gpu_usage,
            'gpu_memory_used': self.gpu_memory_used,
            'gpu_memory_total': self.gpu_memory_total,
            'active_generations': self.active_generations,
            'queue_length': self.queue_length,
            'comfyui_version': self.comfyui_version,
            'frontend_version': self.frontend_version
        }