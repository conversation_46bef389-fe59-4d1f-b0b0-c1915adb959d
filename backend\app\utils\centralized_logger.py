"""
Centralized Error Logging System for ComfyUI Frontend
=====================================================

This module provides centralized error logging that can be accessed by Claude
and other monitoring systems.
"""

import json
import os
import logging
import traceback
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

class CentralizedLogger:
    def __init__(self, log_dir: str = "C:/temp/comfyui_workspace/logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Main error log file
        self.error_log_file = self.log_dir / "system_errors.json"
        self.activity_log_file = self.log_dir / "system_activity.log"
        self.claude_accessible_log = self.log_dir / "claude_errors.json"
        
        # Setup logging
        self.setup_logging()
        
        # Initialize error tracking
        self.current_session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.error_count = 0
        
    def setup_logging(self):
        """Setup Python logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.activity_log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger("ComfyUI_System")
        
    def log_error(self, 
                  component: str, 
                  error_type: str, 
                  message: str, 
                  details: Optional[Dict] = None,
                  exception: Optional[Exception] = None):
        """Log an error with full context"""
        
        self.error_count += 1
        timestamp = datetime.now().isoformat()
        
        error_entry = {
            "session_id": self.current_session_id,
            "error_id": f"{self.current_session_id}_{self.error_count:04d}",
            "timestamp": timestamp,
            "component": component,  # "ComfyUI", "Backend", "Frontend", "System"
            "error_type": error_type,
            "message": message,
            "details": details or {},
            "stack_trace": None,
            "severity": self._determine_severity(error_type)
        }
        
        # Add exception details if provided
        if exception:
            error_entry["exception_type"] = type(exception).__name__
            error_entry["stack_trace"] = traceback.format_exc()
            
        # Log to Python logger
        self.logger.error(f"[{component}] {error_type}: {message}")
        if exception:
            self.logger.error(f"Exception: {exception}")
            
        # Save to JSON error log
        self._append_to_error_log(error_entry)
        
        # Update Claude-accessible summary
        self._update_claude_log(error_entry)
        
        return error_entry["error_id"]
        
    def log_activity(self, component: str, activity: str, details: Optional[Dict] = None):
        """Log normal activity/status updates"""
        timestamp = datetime.now().isoformat()
        
        activity_entry = {
            "session_id": self.current_session_id,
            "timestamp": timestamp,
            "component": component,
            "activity": activity,
            "details": details or {}
        }
        
        self.logger.info(f"[{component}] {activity}")
        
        # Also save significant activities to JSON for Claude
        if self._is_significant_activity(activity):
            self._append_to_activity_log(activity_entry)
            
    def get_recent_errors(self, hours: int = 24, component: Optional[str] = None) -> List[Dict]:
        """Get recent errors for debugging"""
        if not self.error_log_file.exists():
            return []
            
        recent_errors = []
        cutoff_time = datetime.now().timestamp() - (hours * 3600)
        
        try:
            with open(self.error_log_file, 'r') as f:
                for line in f:
                    try:
                        error = json.loads(line.strip())
                        error_time = datetime.fromisoformat(error['timestamp']).timestamp()
                        
                        if error_time >= cutoff_time:
                            if component is None or error['component'] == component:
                                recent_errors.append(error)
                    except (json.JSONDecodeError, KeyError, ValueError):
                        continue
                        
        except FileNotFoundError:
            pass
            
        return sorted(recent_errors, key=lambda x: x['timestamp'], reverse=True)
        
    def get_claude_summary(self) -> Dict:
        """Get error summary specifically formatted for Claude access"""
        try:
            if self.claude_accessible_log.exists():
                with open(self.claude_accessible_log, 'r') as f:
                    return json.load(f)
            return {"status": "no_errors", "session_id": self.current_session_id}
        except Exception as e:
            return {"status": "log_read_error", "error": str(e)}
            
    def _determine_severity(self, error_type: str) -> str:
        """Determine error severity level"""
        critical_types = ["startup_failure", "cuda_error", "port_conflict", "dependency_missing"]
        warning_types = ["generation_timeout", "model_load_warning", "api_slow_response"]
        
        if error_type.lower() in critical_types:
            return "CRITICAL"
        elif error_type.lower() in warning_types:
            return "WARNING"
        else:
            return "ERROR"
            
    def _is_significant_activity(self, activity: str) -> bool:
        """Determine if activity should be logged for Claude"""
        significant = [
            "service_started", "service_stopped", "generation_completed",
            "model_loaded", "cuda_initialized", "api_ready"
        ]
        return any(sig in activity.lower() for sig in significant)
        
    def _append_to_error_log(self, error_entry: Dict):
        """Append error to main error log"""
        try:
            with open(self.error_log_file, 'a') as f:
                f.write(json.dumps(error_entry) + '\n')
        except Exception as e:
            self.logger.error(f"Failed to write to error log: {e}")
            
    def _append_to_activity_log(self, activity_entry: Dict):
        """Append significant activity to log"""
        activity_file = self.log_dir / "significant_activities.json"
        try:
            with open(activity_file, 'a') as f:
                f.write(json.dumps(activity_entry) + '\n')
        except Exception as e:
            self.logger.error(f"Failed to write to activity log: {e}")
            
    def _update_claude_log(self, error_entry: Dict):
        """Update the Claude-accessible error summary"""
        try:
            # Load existing summary
            summary = {"status": "errors_present", "session_id": self.current_session_id}
            if self.claude_accessible_log.exists():
                with open(self.claude_accessible_log, 'r') as f:
                    summary = json.load(f)
                    
            # Update summary
            if "errors" not in summary:
                summary["errors"] = []
                
            # Keep only last 50 errors for Claude
            summary["errors"] = summary["errors"][-49:] + [error_entry]
            summary["last_updated"] = datetime.now().isoformat()
            summary["total_errors"] = len(summary["errors"])
            summary["critical_count"] = len([e for e in summary["errors"] if e["severity"] == "CRITICAL"])
            
            # Save updated summary
            with open(self.claude_accessible_log, 'w') as f:
                json.dump(summary, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Failed to update Claude log: {e}")


# Global logger instance
_global_logger = None

def get_logger() -> CentralizedLogger:
    """Get the global logger instance"""
    global _global_logger
    if _global_logger is None:
        _global_logger = CentralizedLogger()
    return _global_logger

def log_error(component: str, error_type: str, message: str, details: Optional[Dict] = None, exception: Optional[Exception] = None):
    """Convenience function for error logging"""
    return get_logger().log_error(component, error_type, message, details, exception)

def log_activity(component: str, activity: str, details: Optional[Dict] = None):
    """Convenience function for activity logging"""
    return get_logger().log_activity(component, activity, details)

# Example usage and testing
if __name__ == "__main__":
    # Test the logging system
    logger = get_logger()
    
    # Test error logging
    logger.log_error(
        component="Backend",
        error_type="startup_failure",
        message="Failed to connect to ComfyUI server",
        details={"port": 8188, "host": "localhost"},
        exception=ConnectionError("Connection refused")
    )
    
    # Test activity logging
    logger.log_activity(
        component="ComfyUI",
        activity="service_started",
        details={"gpu": "RTX 4070 Ti SUPER", "cuda_version": "12.8"}
    )
    
    print("Logging system test completed. Check logs in:", logger.log_dir)
