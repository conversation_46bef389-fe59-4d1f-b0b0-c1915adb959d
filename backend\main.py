from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import asyncio
import json
from typing import Dict, List
from contextlib import asynccontextmanager
import logging
from app.utils.frontend_log import post_log_to_frontend, set_websocket_manager

from app.api.routes import router as api_router
from app.api.workflow_routes import router as workflow_router

try:
    from routes.generation import router as generation_router
    print("[DEBUG] Successfully imported generation router")
except Exception as e:
    print(f"[ERROR] Failed to import generation router: {e}")
    generation_router = None
from app.core.config import settings
from app.core.database import init_database, health_check as db_health_check
from app.services.comfyui_service import ComfyUIService
from app.services.ollama_service import OllamaService
from app.services.system_monitor import SystemMonitor
from app.services.websocket_relay import websocket_relay

# Configure logging
logging.basicConfig(level=logging.INFO)
logging.getLogger("httpx").setLevel(logging.WARNING)
logger = logging.getLogger(__name__)

# Initialize services
comfyui_service = ComfyUIService()
ollama_service = OllamaService()
system_monitor = SystemMonitor()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handles application startup and shutdown events."""
    logger.info("Starting ComfyUI Custom Frontend API...")
    post_log_to_frontend("Starting ComfyUI Custom Frontend API...")
    
    # Initialize database
    try:
        await init_database()
        logger.info("Database initialized successfully")
        post_log_to_frontend("Database initialized successfully")
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        post_log_to_frontend(f"Database initialization failed: {e}")
    
    # Start WebSocket relay service
    await websocket_relay.start()
    
    # Start system monitoring
    asyncio.create_task(system_monitor.start_monitoring())
    # Check ComfyUI connection
    if await comfyui_service.check_connection():
        logger.info("ComfyUI connection established")
        post_log_to_frontend("ComfyUI connection established")
    else:
        logger.warning("ComfyUI connection failed - check if ComfyUI is running")
        post_log_to_frontend("ComfyUI connection failed - check if ComfyUI is running")
    # Check Ollama connection
    if await ollama_service.check_connection():
        logger.info("Ollama connection established")
        post_log_to_frontend("Ollama connection established")
        models = await ollama_service.list_models()
        logger.info(f"Available Ollama models: {[m['name'] for m in models]}")
        post_log_to_frontend(f"Available Ollama models: {[m['name'] for m in models]}")
    else:
        logger.warning("Ollama connection failed - check if Ollama is running")
        post_log_to_frontend("Ollama connection failed - check if Ollama is running")
    yield
    logger.info("Shutting down ComfyUI Custom Frontend API...")
    post_log_to_frontend("Shutting down ComfyUI Custom Frontend API...")
    await system_monitor.stop_monitoring()
    await websocket_relay.stop()

app = FastAPI(
    title="ComfyUI Custom Frontend API",
    description="Backend API for ComfyUI Custom Frontend with AI-powered features",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(api_router, prefix="/api/v1")

# Include workflow tracking routes
app.include_router(workflow_router)

# Include ComfyUI proxy routes
from app.api.comfyui_proxy import router as comfyui_proxy_router
app.include_router(comfyui_proxy_router, prefix="/api/v1")

if generation_router:
    app.include_router(generation_router, prefix="/api/v1")
    print("[DEBUG] Generation router included successfully")
else:
    print("[ERROR] Generation router not available")

# Test generation endpoint
@app.get("/api/v1/test-generation")
async def test_generation():
    """Test generation endpoint"""
    print("[DEBUG] Test generation endpoint called")
    return {"status": "ok", "message": "Generation endpoint is working"}

# Root level health endpoint (for compatibility)
@app.get("/health")
async def root_health():
    """Root level health check"""
    from app.services.comfyui_service import ComfyUIService
    from app.services.ollama_service import OllamaService
    import time
    
    comfyui_service = ComfyUIService()
    ollama_service = OllamaService()
    
    comfyui_status = await comfyui_service.check_connection()
    ollama_status = await ollama_service.check_connection()
    
    return {
        "status": "healthy",
        "services": {
            "comfyui": "connected" if comfyui_status else "disconnected",
            "ollama": "connected" if ollama_status else "disconnected"
        },
        "timestamp": time.time()
    }

# WebSocket connections manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        # Don't accept here - should be done before calling this method
        self.active_connections.append(websocket)
        logger.info(f"Client connected. Total connections: {len(self.active_connections)}")
        post_log_to_frontend(f"Client connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
        logger.info(f"Client disconnected. Total connections: {len(self.active_connections)}")
        post_log_to_frontend(f"Client disconnected. Total connections: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")
                post_log_to_frontend(f"Error broadcasting message: {e}")

manager = ConnectionManager()

# Set the WebSocket manager for frontend logging
set_websocket_manager(manager)

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint with ComfyUI relay integration"""
    # Accept the connection once
    await websocket.accept()
    
    # Add to both systems after accepting
    await websocket_relay.add_frontend_client(websocket)
    manager.active_connections.append(websocket)
    logger.info(f"Client connected. Total connections: {len(manager.active_connections)}")
    
    try:
        while True:
            data = await websocket.receive_text()
            
            # Handle message through relay first
            await websocket_relay.handle_frontend_message(websocket, data)
            
            # Parse message for local handling
            try:
                message_data = json.loads(data)
                message_type = message_data.get("type")
                
                # Handle local backend messages
                if message_type == "ping":
                    await manager.send_personal_message(
                        json.dumps({"type": "pong", "timestamp": message_data.get("timestamp")}),
                        websocket
                    )
                elif message_type == "subscribe_system_stats":
                    # Send current system stats
                    stats = await system_monitor.get_current_stats()
                    await manager.send_personal_message(
                        json.dumps({"type": "system_stats", "data": stats}),
                        websocket
                    )
                elif message_type == "request_health":
                    # Send health status
                    comfyui_status = await comfyui_service.check_connection()
                    ollama_status = await ollama_service.check_connection()
                    
                    health_data = {
                        "type": "health_status",
                        "data": {
                            "status": "healthy" if comfyui_status and ollama_status else "degraded",
                            "services": {
                                "comfyui": "connected" if comfyui_status else "disconnected",
                                "ollama": "connected" if ollama_status else "disconnected"
                            },
                            "timestamp": asyncio.get_event_loop().time()
                        }
                    }
                    await manager.send_personal_message(json.dumps(health_data), websocket)
                    
            except json.JSONDecodeError:
                # Message was already handled by relay or is invalid
                pass
                
    except WebSocketDisconnect:
        await websocket_relay.remove_frontend_client(websocket)
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        post_log_to_frontend(f"WebSocket error: {e}")
        await websocket_relay.remove_frontend_client(websocket)
        manager.disconnect(websocket)

@app.get("/")
async def root():
    return {
        "message": "ComfyUI Custom Frontend API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    comfyui_status = await comfyui_service.check_connection()
    ollama_status = await ollama_service.check_connection()
    
    # Check database health
    db_health = await db_health_check()
    
    return {
        "status": "healthy",
        "services": {
            "comfyui": "connected" if comfyui_status else "disconnected",
            "ollama": "connected" if ollama_status else "disconnected",
            "database": db_health["status"]
        },
        "system": await system_monitor.get_current_stats(),
        "database": db_health
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
