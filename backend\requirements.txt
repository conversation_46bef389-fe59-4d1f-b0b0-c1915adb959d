fastapi==0.115.6
uvicorn[standard]==0.32.1
python-multipart==0.0.12
websockets==13.1
aiofiles==24.1.0
httpx==0.27.2
pillow==11.0.0
numpy==2.2.1
pydantic==2.11.0
pydantic-settings==2.7.0
python-socketio==5.11.4
python-engineio>=4.8.0
requests==2.32.3
asyncio-mqtt==0.16.2
watchfiles==1.0.3
python-dotenv==1.0.1
psutil==6.1.0
GPUtil==1.4.0
ollama==0.4.4
aiohttp==3.11.11
# PyTorch with CUDA support - install separately with:
# pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128
# torch>=2.8.0+cu128
# torchvision>=0.23.0+cu128  
# torchaudio>=2.8.0+cu128
ruff
aiosqlite==0.20.0
sqlalchemy[asyncio]==2.0.36
alembic==1.14.0