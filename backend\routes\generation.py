"""
Generation API Routes
Handles text-to-image generation requests with ComfyUI integration and centralized logging
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
import base64
from io import BytesIO
from PIL import Image

from app.services.comfyui_service import ComfyUIService
from app.services.websocket_relay import websocket_relay
from app.utils.centralized_logger import get_logger, log_error, log_activity

router = APIRouter()
comfyui_service = ComfyUIService()
logger = get_logger()

class GenerationSettings(BaseModel):
    prompt: str
    negative_prompt: str = ""
    model: str
    sampler: str = "euler"
    steps: int = 25
    cfg_scale: float = 7.0
    width: int = 512
    height: int = 512
    seed: int = -1
    lora: Optional[str] = None
    lora_strength: float = 1.0
    lora_keywords: Optional[str] = None
    vae: Optional[str] = None
    batch_size: int = 1
    enable_gpu_acceleration: bool = True
    memory_management: Dict[str, Any] = Field(default_factory=dict)
    scheduler: Optional[str] = None
    guidance_scale: Optional[float] = None
    denoise_strength: float = 1.0
    clip_skip: int = 1
    model_type: str = "checkpoint"
    model_path: str = ""
    generation_id: str = ""
    timestamp: int = 0

class HardwareConfig(BaseModel):
    vram_gb: int = 16
    gpu_model: str = "RTX 4070 Ti SUPER"
    optimization_level: str = "balanced"

class GenerationMetadata(BaseModel):
    frontend_version: str = "1.0.0"
    generation_timestamp: int = 0
    user_agent: str = ""

class GenerationPayload(BaseModel):
    settings: GenerationSettings
    workflow_type: str = "text_to_image"
    hardware_config: HardwareConfig
    metadata: GenerationMetadata

class GenerationResponse(BaseModel):
    success: bool
    generation_id: str
    message: str
    queue_position: Optional[int] = None
    estimated_time: Optional[int] = None

# Enhanced generation state tracking
class GenerationState:
    QUEUED = "queued"
    INITIALIZING = "initializing"
    PROCESSING = "processing"
    RENDERING = "rendering"
    COMPLETING = "completing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

# Store active generations with enhanced state
active_generations: Dict[str, Dict[str, Any]] = {}

@router.get("/test")
async def test_route():
    """Test route to verify the router is working"""
    print("[DEBUG] Test route called")
    log_activity("GENERATION_API", "Test route called")
    return {"status": "ok", "message": "Generation router is working"}

@router.post("/generation/start", response_model=GenerationResponse)
async def generate_image(payload: GenerationPayload, background_tasks: BackgroundTasks):
    """Start image generation process with enhanced state tracking"""

    generation_id = payload.settings.generation_id or str(uuid.uuid4())
    
    # Enhanced logging
    log_activity("GENERATION", f"New generation request received: {generation_id}", {
        "model": payload.settings.model,
        "workflow_type": payload.workflow_type,
        "dimensions": f"{payload.settings.width}x{payload.settings.height}",
        "steps": payload.settings.steps,
        "prompt_length": len(payload.settings.prompt)
    })

    try:
        # Validate settings with enhanced checks
        if not payload.settings.prompt.strip():
            error_msg = "Prompt cannot be empty"
            log_error("GENERATION", "validation_failed", error_msg, {"generation_id": generation_id})
            raise HTTPException(status_code=400, detail=error_msg)

        if not payload.settings.model.strip():
            error_msg = "Model must be specified"
            log_error("GENERATION", "validation_failed", error_msg, {"generation_id": generation_id})
            raise HTTPException(status_code=400, detail=error_msg)

        # Enhanced payload validation
        if payload.settings.width <= 0 or payload.settings.height <= 0:
            error_msg = "Width and height must be positive integers"
            log_error("GENERATION", "validation_failed", error_msg, {"generation_id": generation_id})
            raise HTTPException(status_code=400, detail=error_msg)

        if payload.settings.steps <= 0 or payload.settings.steps > 150:
            error_msg = "Steps must be between 1 and 150"
            log_error("GENERATION", "validation_failed", error_msg, {"generation_id": generation_id})
            raise HTTPException(status_code=400, detail=error_msg)

        # Set the generation_id in settings for workflow creation
        payload.settings.generation_id = generation_id

        # Create ComfyUI workflow with error handling
        try:
            workflow = comfyui_service.build_text_to_image_workflow(
                prompt=payload.settings.prompt,
                negative_prompt=payload.settings.negative_prompt,
                model=payload.settings.model,
                width=payload.settings.width,
                height=payload.settings.height,
                steps=payload.settings.steps,
                cfg=payload.settings.cfg_scale,
                seed=payload.settings.seed
            )
            
            log_activity("GENERATION", f"Workflow created successfully for {generation_id}", {
                "node_count": len(workflow) if isinstance(workflow, dict) else 0
            })
            
        except Exception as workflow_error:
            error_msg = f"Failed to create workflow: {str(workflow_error)}"
            log_error("GENERATION", "workflow_creation_failed", error_msg, {
                "generation_id": generation_id,
                "model": payload.settings.model
            }, workflow_error)
            raise HTTPException(status_code=500, detail=error_msg)

        # Store enhanced generation info with state tracking
        active_generations[generation_id] = {
            "status": GenerationState.QUEUED,
            "state_history": [
                {"state": GenerationState.QUEUED, "timestamp": datetime.now(), "message": "Generation queued"}
            ],
            "settings": payload.settings.dict(),
            "workflow": workflow,
            "hardware_config": payload.hardware_config.dict(),
            "metadata": payload.metadata.dict(),
            "created_at": datetime.now(),
            "progress": 0,
            "stage": "queued",
            "substage": "",
            "error_count": 0,
            "retry_count": 0
        }
        
        # Broadcast initial state to WebSocket clients
        await websocket_relay.broadcast_generation_state(
            generation_id, 
            GenerationState.QUEUED.value, 
            {
                "progress": 0,
                "stage": "queued",
                "message": "Generation queued successfully",
                "queue_position": len([g for g in active_generations.values() if g["status"] in [GenerationState.QUEUED, GenerationState.PROCESSING]])
            }
        )
        
        # Start background processing
        background_tasks.add_task(process_generation_enhanced, generation_id, workflow, payload)

        log_activity("GENERATION", f"Generation {generation_id} queued successfully")

        return GenerationResponse(
            success=True,
            generation_id=generation_id,
            message="Generation started successfully",
            queue_position=len([g for g in active_generations.values() if g["status"] in [GenerationState.QUEUED, GenerationState.PROCESSING]]),
            estimated_time=30
        )
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"Failed to start generation: {str(e)}"
        log_error("GENERATION", "start_failed", error_msg, {"generation_id": generation_id}, e)
        raise HTTPException(status_code=500, detail=error_msg)

async def process_generation_enhanced(generation_id: str, workflow: Dict[str, Any], payload: GenerationPayload):
    """Enhanced generation processing with detailed state tracking and error handling"""

    log_activity("GENERATION", f"Starting enhanced processing for {generation_id}")

    def update_generation_state(new_state: str, message: str, progress: int = None, substage: str = ""):
        """Update generation state with logging and WebSocket broadcasting"""
        if generation_id in active_generations:
            active_generations[generation_id]["status"] = new_state
            active_generations[generation_id]["stage"] = new_state
            active_generations[generation_id]["substage"] = substage
            if progress is not None:
                active_generations[generation_id]["progress"] = progress
            
            # Add to state history
            state_entry = {
                "state": new_state,
                "timestamp": datetime.now(),
                "message": message,
                "progress": progress,
                "substage": substage
            }
            active_generations[generation_id]["state_history"].append(state_entry)
            
            log_activity("GENERATION", f"State update for {generation_id}: {new_state} - {message}", {
                "progress": progress,
                "substage": substage
            })
            
            # Broadcast state update via WebSocket
            asyncio.create_task(websocket_relay.broadcast_generation_progress(
                generation_id=generation_id,
                state=new_state,
                progress=progress or 0,
                substage=substage,
                message=message
            ))

    try:
        # State 1: Initializing
        update_generation_state(GenerationState.INITIALIZING, "Initializing generation process", 5)

        # Check ComfyUI connection
        if not await comfyui_service.check_connection():
            error_msg = "ComfyUI server is not available"
            log_error("GENERATION", "comfyui_unavailable", error_msg, {"generation_id": generation_id})
            update_generation_state(GenerationState.FAILED, error_msg)
            return

        # State 2: Processing - Submit workflow
        update_generation_state(GenerationState.PROCESSING, "Submitting workflow to ComfyUI", 10, "workflow_submission")
        
        prompt_id = await comfyui_service.submit_workflow(workflow)
        
        if not prompt_id:
            error_msg = "No prompt_id returned from ComfyUI"
            log_error("GENERATION", "workflow_submission_failed", error_msg, {"generation_id": generation_id})
            update_generation_state(GenerationState.FAILED, error_msg)
            return
        
        # Store the prompt_id for tracking and cancellation
        active_generations[generation_id]["prompt_id"] = prompt_id
        active_generations[generation_id]["comfyui_prompt_id"] = prompt_id
        
        log_activity("GENERATION", f"Workflow submitted successfully", {
            "generation_id": generation_id,
            "prompt_id": prompt_id
        })

        # State 3: Processing - Monitor progress
        update_generation_state(GenerationState.PROCESSING, "Monitoring generation progress", 15, "progress_monitoring")
        
        # Enhanced progress monitoring
        await monitor_comfyui_progress_enhanced(generation_id, prompt_id, update_generation_state)
            
    except asyncio.CancelledError:
        log_activity("GENERATION", f"Generation {generation_id} was cancelled")
        update_generation_state(GenerationState.CANCELLED, "Generation cancelled by user")
    except Exception as e:
        error_msg = f"Generation processing failed: {str(e)}"
        log_error("GENERATION", "processing_failed", error_msg, {"generation_id": generation_id}, e)
        
        # Increment error count for retry logic
        if generation_id in active_generations:
            active_generations[generation_id]["error_count"] += 1
            active_generations[generation_id]["last_error"] = error_msg
            active_generations[generation_id]["last_error_time"] = datetime.now()
        
        update_generation_state(GenerationState.FAILED, error_msg)

async def monitor_comfyui_progress_enhanced(generation_id: str, prompt_id: str, update_state_func):
    """Enhanced ComfyUI progress monitoring with detailed state tracking"""

    log_activity("GENERATION", f"Starting enhanced monitoring for {generation_id}", {"prompt_id": prompt_id})

    try:
        timeout_seconds = 1800  # 30 minutes total timeout
        start_time = datetime.now()
        last_status = None
        consecutive_errors = 0
        max_consecutive_errors = 5

        while True:
            elapsed = (datetime.now() - start_time).total_seconds()
            if elapsed > timeout_seconds:
                error_msg = f"Generation timed out after {elapsed:.1f} seconds"
                log_error("GENERATION", "generation_timeout", error_msg, {
                    "generation_id": generation_id,
                    "prompt_id": prompt_id,
                    "elapsed_seconds": elapsed
                })
                update_state_func(GenerationState.FAILED, error_msg)
                break

            try:
                status_data = await comfyui_service.get_workflow_status(prompt_id)
                status = status_data.get("status")
                consecutive_errors = 0  # Reset error count on successful status check

                # Enhanced status handling
                if status != last_status:
                    log_activity("GENERATION", f"Status change for {generation_id}: {last_status} -> {status}")
                    last_status = status

                if status == "completed":
                    log_activity("GENERATION", f"ComfyUI execution completed for {generation_id}")
                    update_state_func(GenerationState.COMPLETING, "Processing results", 90, "result_processing")
                    await handle_generation_complete_enhanced(generation_id, prompt_id, update_state_func)
                    break
                elif status == "failed":
                    error_msg = status_data.get("error", "Unknown execution error")
                    log_error("GENERATION", "comfyui_execution_failed", error_msg, {
                        "generation_id": generation_id,
                        "prompt_id": prompt_id
                    })
                    update_state_func(GenerationState.FAILED, f"ComfyUI execution failed: {error_msg}")
                    # Broadcast error via WebSocket
                    await websocket_relay.broadcast_generation_error(
                        generation_id, 
                        f"ComfyUI execution failed: {error_msg}",
                        "EXECUTION_FAILED",
                        {"prompt_id": prompt_id, "status_data": status_data}
                    )
                    break
                elif status == "queued":
                    queue_position = status_data.get("queue_position", 0)
                    update_state_func(GenerationState.PROCESSING, f"In queue (position {queue_position})", 20, "queued")
                elif status == "processing":
                    # Try to extract more detailed progress if available
                    progress = min(85, 25 + int(elapsed / 30))  # Gradual progress simulation
                    update_state_func(GenerationState.PROCESSING, "Generating image", progress, "rendering")
                elif status == "error":
                    error_msg = status_data.get("error", "ComfyUI reported an error")
                    log_error("GENERATION", "comfyui_status_error", error_msg, {
                        "generation_id": generation_id,
                        "prompt_id": prompt_id
                    })
                    update_state_func(GenerationState.FAILED, error_msg)
                    # Broadcast error via WebSocket
                    await websocket_relay.broadcast_generation_error(
                        generation_id, 
                        error_msg,
                        "STATUS_ERROR",
                        {"prompt_id": prompt_id, "status_data": status_data}
                    )
                    break
                else:
                    # Unknown status - log for debugging
                    log_activity("GENERATION", f"Unknown status for {generation_id}: {status}", {
                        "status_data": status_data
                    })

            except Exception as status_error:
                consecutive_errors += 1
                error_msg = f"Error checking status: {str(status_error)}"
                
                if consecutive_errors >= max_consecutive_errors:
                    log_error("GENERATION", "status_check_failed", f"Too many consecutive errors ({consecutive_errors}): {error_msg}", {
                        "generation_id": generation_id,
                        "prompt_id": prompt_id
                    })
                    update_state_func(GenerationState.FAILED, "Lost connection to ComfyUI")
                    break
                else:
                    log_activity("GENERATION", f"Status check error for {generation_id} (attempt {consecutive_errors}): {error_msg}")

            # Adaptive polling interval
            poll_interval = 2 if status in ["processing", "rendering"] else 5
            await asyncio.sleep(poll_interval)

    except Exception as e:
        error_msg = f"Error monitoring progress: {str(e)}"
        log_error("GENERATION", "monitoring_failed", error_msg, {
            "generation_id": generation_id,
            "prompt_id": prompt_id
        }, e)
        update_state_func(GenerationState.FAILED, error_msg)

async def handle_generation_complete_enhanced(generation_id: str, prompt_id: str, update_state_func):
    """Enhanced generation completion handler with detailed result processing"""

    try:
        log_activity("GENERATION", f"Processing completion for {generation_id}")
        update_state_func(GenerationState.COMPLETING, "Retrieving generated images", 95, "image_retrieval")

        # Try to get the actual generated image with retries
        max_retries = 3
        images = None
        
        for attempt in range(max_retries):
            try:
                images = await comfyui_service.get_generated_images(prompt_id)
                if images:
                    break
                else:
                    log_activity("GENERATION", f"No images found for {generation_id}, attempt {attempt + 1}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(2)  # Wait before retry
            except Exception as img_error:
                log_activity("GENERATION", f"Image retrieval attempt {attempt + 1} failed for {generation_id}: {str(img_error)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)
        
        if images:
            image_info = images[0]
            log_activity("GENERATION", f"Retrieved image info for {generation_id}", {
                "filename": image_info["filename"],
                "subfolder": image_info.get("subfolder", ""),
                "type": image_info.get("type", "output")
            })
            
            # Download image data with error handling
            try:
                image_bytes = await comfyui_service.download_image(
                    image_info["filename"], 
                    image_info.get("subfolder", ""), 
                    image_info.get("type", "output")
                )
                
                if image_bytes:
                    # Process image data
                    image_data = base64.b64encode(image_bytes).decode('utf-8')
                    file_ext = image_info["filename"].lower().split('.')[-1]
                    mime_type = f'image/{file_ext}' if file_ext in ['jpeg', 'jpg', 'png', 'webp'] else 'image/png'
                    
                    # Store enhanced result data
                    active_generations[generation_id]["image_data"] = f"data:{mime_type};base64,{image_data}"
                    active_generations[generation_id]["image_info"] = {
                        "filename": image_info["filename"],
                        "size_bytes": len(image_bytes),
                        "mime_type": mime_type,
                        "dimensions": None  # Could be extracted from image_bytes if needed
                    }
                    
                    log_activity("GENERATION", f"Successfully processed image for {generation_id}", {
                        "file_size": len(image_bytes),
                        "mime_type": mime_type
                    })
                else:
                    raise Exception("Downloaded image data is empty")
                    
            except Exception as download_error:
                error_msg = f"Failed to download image: {str(download_error)}"
                log_error("GENERATION", "image_download_failed", error_msg, {
                    "generation_id": generation_id,
                    "image_info": image_info
                }, download_error)
                
                # Use placeholder on download failure
                placeholder_image_data = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
                active_generations[generation_id]["image_data"] = placeholder_image_data
                active_generations[generation_id]["image_info"] = {
                    "filename": "placeholder.png",
                    "size_bytes": 0,
                    "mime_type": "image/png",
                    "is_placeholder": True,
                    "error": error_msg
                }
        else:
            # No images found - use placeholder
            log_activity("GENERATION", f"No images found for {generation_id}, using placeholder")
            placeholder_image_data = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            active_generations[generation_id]["image_data"] = placeholder_image_data
            active_generations[generation_id]["image_info"] = {
                "filename": "placeholder.png",
                "size_bytes": 0,
                "mime_type": "image/png",
                "is_placeholder": True,
                "error": "No images generated"
            }

        # Final completion
        active_generations[generation_id]["completed_at"] = datetime.now()
        active_generations[generation_id]["processing_time"] = (
            datetime.now() - active_generations[generation_id]["created_at"]
        ).total_seconds()
        
        update_state_func(GenerationState.COMPLETED, "Generation completed successfully", 100, "completed")
        
        # Broadcast completion via WebSocket
        completion_data = {
            "processing_time": active_generations[generation_id]["processing_time"],
            "has_image": "image_data" in active_generations[generation_id],
            "image_count": len(active_generations[generation_id].get("image_data", [])) if isinstance(active_generations[generation_id].get("image_data"), list) else (1 if active_generations[generation_id].get("image_data") else 0)
        }
        
        await websocket_relay.broadcast_generation_complete(
            generation_id,
            completion_data,
            {"prompt_id": prompt_id}
        )
        
        log_activity("GENERATION", f"Generation {generation_id} completed successfully", {
            "processing_time": active_generations[generation_id]["processing_time"],
            "has_image": "image_data" in active_generations[generation_id]
        })

    except Exception as e:
        error_msg = f"Failed to process completion: {str(e)}"
        log_error("GENERATION", "completion_processing_failed", error_msg, {
            "generation_id": generation_id,
            "prompt_id": prompt_id
        }, e)
        
        if generation_id in active_generations:
            active_generations[generation_id]["completion_error"] = error_msg
        
        update_state_func(GenerationState.FAILED, error_msg)

@router.get("/generation/{generation_id}/status")
async def get_generation_status(generation_id: str):
    """Get enhanced generation status with detailed progress information"""
    
    if generation_id not in active_generations:
        log_activity("GENERATION", f"Status request for unknown generation: {generation_id}")
        raise HTTPException(status_code=404, detail="Generation not found")
    
    generation = active_generations[generation_id]
    
    # Calculate processing time
    processing_time = None
    if generation.get("completed_at"):
        processing_time = (generation["completed_at"] - generation["created_at"]).total_seconds()
    elif generation["status"] not in [GenerationState.COMPLETED, GenerationState.FAILED, GenerationState.CANCELLED]:
        processing_time = (datetime.now() - generation["created_at"]).total_seconds()
    
    # Build enhanced status response
    status_response = {
        "generation_id": generation_id,
        "status": generation["status"],
        "stage": generation.get("stage", generation["status"]),
        "substage": generation.get("substage", ""),
        "progress": generation.get("progress", 0),
        "image_data": generation.get("image_data"),
        "error": generation.get("error") or generation.get("last_error"),
        "created_at": generation["created_at"].isoformat(),
        "completed_at": generation.get("completed_at").isoformat() if generation.get("completed_at") else None,
        "processing_time": processing_time,
        "retry_count": generation.get("retry_count", 0),
        "error_count": generation.get("error_count", 0),
        "comfyui_prompt_id": generation.get("comfyui_prompt_id"),
        "image_info": generation.get("image_info"),
        "state_history": [
            {
                "state": entry["state"],
                "timestamp": entry["timestamp"].isoformat(),
                "message": entry["message"],
                "progress": entry.get("progress"),
                "substage": entry.get("substage", "")
            } for entry in generation.get("state_history", [])
        ]
    }
    
    # Log status request
    log_activity("GENERATION", f"Status requested for {generation_id}", {
        "current_status": generation["status"],
        "progress": generation.get("progress", 0)
    })
    
    return status_response

@router.post("/generation/{generation_id}/cancel")
async def cancel_generation(generation_id: str):
    """Cancel generation with enhanced cleanup"""
    
    if generation_id not in active_generations:
        raise HTTPException(status_code=404, detail="Generation not found")
    
    generation = active_generations[generation_id]
    
    log_activity("GENERATION", f"Cancel requested for {generation_id}", {
        "current_status": generation["status"]
    })
    
    # Check if generation can be cancelled
    if generation["status"] in [GenerationState.COMPLETED, GenerationState.FAILED, GenerationState.CANCELLED]:
        return {"success": False, "message": f"Generation is already {generation['status']}"}
    
    try:
        # Try to cancel in ComfyUI if we have a prompt_id
        comfyui_prompt_id = generation.get("comfyui_prompt_id")
        if comfyui_prompt_id:
            try:
                # Note: This requires implementing cancel_workflow in comfyui_service
                # await comfyui_service.cancel_workflow(comfyui_prompt_id)
                log_activity("GENERATION", f"ComfyUI cancellation attempted for {generation_id}")
            except Exception as e:
                log_activity("GENERATION", f"ComfyUI cancellation failed for {generation_id}: {str(e)}")
    
        # Update local status
        generation["status"] = GenerationState.CANCELLED
        generation["cancelled_at"] = datetime.now()
        generation["state_history"].append({
            "state": GenerationState.CANCELLED,
            "timestamp": datetime.now(),
            "message": "Generation cancelled by user request",
            "progress": generation.get("progress", 0)
        })
        
        log_activity("GENERATION", f"Generation {generation_id} cancelled successfully")
        
        return {"success": True, "message": "Generation cancelled"}
        
    except Exception as e:
        error_msg = f"Failed to cancel generation: {str(e)}"
        log_error("GENERATION", "cancellation_failed", error_msg, {"generation_id": generation_id}, e)
        raise HTTPException(status_code=500, detail=error_msg)

@router.get("/generation/active")
async def get_active_generations():
    """Get all active generations with enhanced information"""
    
    current_time = datetime.now()
    
    active_list = []
    for gen_id, gen_data in active_generations.items():
        if gen_data["status"] not in [GenerationState.COMPLETED, GenerationState.FAILED, GenerationState.CANCELLED]:
            processing_time = (current_time - gen_data["created_at"]).total_seconds()
            
            active_list.append({
                "generation_id": gen_id,
                "status": gen_data["status"],
                "stage": gen_data.get("stage", gen_data["status"]),
                "substage": gen_data.get("substage", ""),
                "progress": gen_data.get("progress", 0),
                "created_at": gen_data["created_at"].isoformat(),
                "processing_time": processing_time,
                "model": gen_data.get("settings", {}).get("model", "unknown"),
                "prompt_preview": gen_data.get("settings", {}).get("prompt", "")[:100] + "..." if len(gen_data.get("settings", {}).get("prompt", "")) > 100 else gen_data.get("settings", {}).get("prompt", "")
            })
    
    log_activity("GENERATION", f"Active generations requested: {len(active_list)} found")
    
    return {
        "active_count": len(active_list),
        "active_generations": active_list,
        "total_tracked": len(active_generations)
    }

@router.get("/generation/history")
async def get_generation_history(limit: int = 50, status_filter: Optional[str] = None):
    """Get generation history with filtering"""
    
    history_list = []
    
    for gen_id, gen_data in active_generations.items():
        # Apply status filter if specified
        if status_filter and gen_data["status"] != status_filter:
            continue
            
        processing_time = None
        if gen_data.get("completed_at"):
            processing_time = (gen_data["completed_at"] - gen_data["created_at"]).total_seconds()
        
        history_entry = {
            "generation_id": gen_id,
            "status": gen_data["status"],
            "created_at": gen_data["created_at"].isoformat(),
            "completed_at": gen_data.get("completed_at").isoformat() if gen_data.get("completed_at") else None,
            "processing_time": processing_time,
            "model": gen_data.get("settings", {}).get("model", "unknown"),
            "prompt_preview": gen_data.get("settings", {}).get("prompt", "")[:100] + "..." if len(gen_data.get("settings", {}).get("prompt", "")) > 100 else gen_data.get("settings", {}).get("prompt", ""),
            "error": gen_data.get("error") or gen_data.get("last_error"),
            "has_image": "image_data" in gen_data
        }
        
        history_list.append(history_entry)
    
    # Sort by creation time (newest first) and limit
    history_list.sort(key=lambda x: x["created_at"], reverse=True)
    history_list = history_list[:limit]
    
    log_activity("GENERATION", f"Generation history requested: {len(history_list)} entries returned")
    
    return {
        "total_generations": len(active_generations),
        "returned_count": len(history_list),
        "history": history_list
    }

@router.delete("/generation/{generation_id}")
async def delete_generation(generation_id: str):
    """Delete a generation record"""
    
    if generation_id not in active_generations:
        raise HTTPException(status_code=404, detail="Generation not found")
    
    generation = active_generations[generation_id]
    
    # Don't allow deletion of active generations
    if generation["status"] not in [GenerationState.COMPLETED, GenerationState.FAILED, GenerationState.CANCELLED]:
        raise HTTPException(status_code=400, detail="Cannot delete active generation")
    
    del active_generations[generation_id]
    
    log_activity("GENERATION", f"Generation {generation_id} deleted")
    
    return {"success": True, "message": "Generation deleted"}

@router.get("/generation/stats")
async def get_generation_stats():
    """Get generation statistics"""
    
    total_generations = len(active_generations)
    
    status_counts = {}
    total_processing_time = 0
    completed_count = 0
    
    for gen_data in active_generations.values():
        status = gen_data["status"]
        status_counts[status] = status_counts.get(status, 0) + 1
        
        if gen_data.get("completed_at") and status == GenerationState.COMPLETED:
            processing_time = (gen_data["completed_at"] - gen_data["created_at"]).total_seconds()
            total_processing_time += processing_time
            completed_count += 1
    
    avg_processing_time = total_processing_time / completed_count if completed_count > 0 else 0
    
    stats = {
        "total_generations": total_generations,
        "status_breakdown": status_counts,
        "completed_count": completed_count,
        "success_rate": (status_counts.get(GenerationState.COMPLETED, 0) / total_generations * 100) if total_generations > 0 else 0,
        "average_processing_time": avg_processing_time,
        "active_count": len([g for g in active_generations.values() if g["status"] not in [GenerationState.COMPLETED, GenerationState.FAILED, GenerationState.CANCELLED]])
    }
    
    log_activity("GENERATION", "Generation statistics requested", stats)
    
    return stats
            }
            for gen_id, gen_data in active_generations.items()
            if gen_data["status"] in ["queued", "processing"]
        ]
    }

@router.get("/generation/nvidia-status")
async def get_nvidia_optimization_status():
    """Get NVIDIA optimization status and performance metrics"""
    
    try:
        # Try to get GPU info from system
        import subprocess
        import json as json_module
        
        try:
            # Try nvidia-smi to get GPU info
            result = subprocess.run([
                'nvidia-smi', 
                '--query-gpu=name,driver_version,memory.total,memory.used,temperature.gpu,utilization.gpu',
                '--format=csv,noheader,nounits'
            ], capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                gpu_info = result.stdout.strip().split(', ')
                return {
                    "nvidia_available": True,
                    "gpu_name": gpu_info[0] if len(gpu_info) > 0 else "Unknown",
                    "driver_version": gpu_info[1] if len(gpu_info) > 1 else "Unknown",
                    "vram_total_mb": int(gpu_info[2]) if len(gpu_info) > 2 and gpu_info[2].isdigit() else 0,
                    "vram_used_mb": int(gpu_info[3]) if len(gpu_info) > 3 and gpu_info[3].isdigit() else 0,
                    "temperature_c": int(gpu_info[4]) if len(gpu_info) > 4 and gpu_info[4].isdigit() else 0,
                    "utilization_percent": int(gpu_info[5]) if len(gpu_info) > 5 and gpu_info[5].isdigit() else 0,
                    "optimization_status": "optimal" if "RTX 4070 Ti SUPER" in gpu_info[0] else "suboptimal",
                    "recommendations": []
                }
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            pass
        
        # Fallback if nvidia-smi not available
        return {
            "nvidia_available": False,
            "message": "NVIDIA GPU information not available",
            "optimization_status": "unknown",
            "recommendations": [
                "Install NVIDIA drivers",
                "Ensure nvidia-smi is available in PATH",
                "Verify RTX 4070 Ti SUPER is properly installed"
            ]
        }
        
    except Exception as e:
        return {
            "nvidia_available": False,
            "error": str(e),
            "optimization_status": "error"
        }

@router.post("/cancel/{generation_id}")
async def cancel_generation(generation_id: str):
    """Cancel an active generation"""
    print(f"[DEBUG] Cancel request received for generation_id: {generation_id}")
    
    try:
        # Check if generation exists in our tracking
        if generation_id in active_generations:
            generation_data = active_generations[generation_id]
            prompt_id = generation_data.get("prompt_id")
            
            # Cancel in ComfyUI if we have a prompt_id
            if prompt_id:
                try:
                    await comfyui_service.cancel_generation(prompt_id)
                    print(f"[DEBUG] ComfyUI cancellation requested for prompt_id: {prompt_id}")
                except Exception as e:
                    print(f"[DEBUG] ComfyUI cancellation failed: {e}")
            
            # Remove from active generations
            del active_generations[generation_id]
            print(f"[DEBUG] Generation {generation_id} removed from active tracking")
        
        # Always try to cleanup ComfyUI queue/memory regardless
        try:
            await comfyui_service.clear_queue()
            await comfyui_service.free_memory()
            print(f"[DEBUG] ComfyUI queue cleared and memory freed")
        except Exception as e:
            print(f"[DEBUG] ComfyUI cleanup failed: {e}")
        
        return {
            "success": True,
            "message": f"Generation {generation_id} cancelled successfully",
            "generation_id": generation_id
        }
        
    except Exception as e:
        print(f"[ERROR] Cancel generation failed: {e}")
        # Still try to cleanup ComfyUI
        try:
            await comfyui_service.clear_queue()
            await comfyui_service.free_memory()
        except:
            pass
            
        return {
            "success": True,  # Return success even on errors to ensure frontend cleanup
            "message": f"Generation cancelled (forced cleanup)",
            "generation_id": generation_id,
            "warning": str(e)
        }
