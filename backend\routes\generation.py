"""
Generation API Routes
Handles text-to-image generation requests with ComfyUI integration
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
import base64
from io import BytesIO
from PIL import Image

from app.services.comfyui_service import ComfyUIService

router = APIRouter()
comfyui_service = ComfyUIService()

class GenerationSettings(BaseModel):
    prompt: str
    negative_prompt: str = ""
    model: str
    sampler: str = "euler"
    steps: int = 25
    cfg_scale: float = 7.0
    width: int = 512
    height: int = 512
    seed: int = -1
    lora: Optional[str] = None
    lora_strength: float = 1.0
    lora_keywords: Optional[str] = None
    vae: Optional[str] = None
    batch_size: int = 1
    enable_gpu_acceleration: bool = True
    memory_management: Dict[str, Any] = Field(default_factory=dict)
    scheduler: Optional[str] = None
    guidance_scale: Optional[float] = None
    denoise_strength: float = 1.0
    clip_skip: int = 1
    model_type: str = "checkpoint"
    model_path: str = ""
    generation_id: str = ""
    timestamp: int = 0

class HardwareConfig(BaseModel):
    vram_gb: int = 16
    gpu_model: str = "RTX 4070 Ti SUPER"
    optimization_level: str = "balanced"

class GenerationMetadata(BaseModel):
    frontend_version: str = "1.0.0"
    generation_timestamp: int = 0
    user_agent: str = ""

class GenerationPayload(BaseModel):
    settings: GenerationSettings
    workflow_type: str = "text_to_image"
    hardware_config: HardwareConfig
    metadata: GenerationMetadata

class GenerationResponse(BaseModel):
    success: bool
    generation_id: str
    message: str
    queue_position: Optional[int] = None
    estimated_time: Optional[int] = None

# Store active generations
active_generations: Dict[str, Dict[str, Any]] = {}

@router.get("/test")
async def test_route():
    """Test route to verify the router is working"""
    print("[DEBUG] Test route called")
    return {"status": "ok", "message": "Generation router is working"}

@router.post("/generation/start", response_model=GenerationResponse)
async def generate_image(payload: GenerationPayload, background_tasks: BackgroundTasks):
    """Start image generation process"""

    generation_id = payload.settings.generation_id or str(uuid.uuid4())
    print(f"[DEBUG] Generation request received for {generation_id}")
    print(f"[DEBUG] Model: {payload.settings.model}")
    print(f"[DEBUG] Prompt: {payload.settings.prompt}")

    try:
        # Validate settings
        if not payload.settings.prompt.strip():
            raise HTTPException(status_code=400, detail="Prompt cannot be empty")

        if not payload.settings.model.strip():
            raise HTTPException(status_code=400, detail="Model must be specified")

        # Set the generation_id in settings for workflow creation
        payload.settings.generation_id = generation_id

        # Create ComfyUI workflow
        workflow = comfyui_service.build_text_to_image_workflow(
            prompt=payload.settings.prompt,
            negative_prompt=payload.settings.negative_prompt,
            model=payload.settings.model,
            width=payload.settings.width,
            height=payload.settings.height,
            steps=payload.settings.steps,
            cfg=payload.settings.cfg_scale,
            seed=payload.settings.seed
        )
        print(f"[DEBUG] Generated workflow for {generation_id}:")
        print(json.dumps(workflow, indent=2))

        # Store generation info
        active_generations[generation_id] = {
            "status": "queued",
            "settings": payload.settings.dict(),
            "workflow": workflow,
            "created_at": datetime.now(),
            "progress": 0
        }
        
        background_tasks.add_task(process_generation, generation_id, workflow)

        return GenerationResponse(
            success=True,
            generation_id=generation_id,
            message="Generation started successfully",
            queue_position=1,
            estimated_time=30
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start generation: {str(e)}")

async def process_generation(generation_id: str, workflow: Dict[str, Any]):
    """Process generation with ComfyUI"""

    print(f"[DEBUG] process_generation started for {generation_id}")

    try:
        # Update status
        active_generations[generation_id]["status"] = "processing"
        
        # Send workflow to ComfyUI
        prompt_id = await comfyui_service.submit_workflow(workflow)
        
        if not prompt_id:
            raise Exception("No prompt_id returned from ComfyUI")
        
        # Store the prompt_id for cancellation purposes
        active_generations[generation_id]["prompt_id"] = prompt_id
        print(f"[DEBUG] Stored prompt_id {prompt_id} for generation_id {generation_id}")
        
        # Monitor progress
        await monitor_comfyui_progress(generation_id, prompt_id)
            
    except Exception as e:
        active_generations[generation_id]["status"] = "failed"
        active_generations[generation_id]["error"] = str(e)
        print(f"Generation {generation_id} failed: {e}")

async def monitor_comfyui_progress(generation_id: str, prompt_id: str):
    """Monitor ComfyUI progress"""

    print(f"[DEBUG] Starting monitoring for generation {generation_id}, prompt {prompt_id}")

    try:
        timeout_seconds = 1800  # 30 minutes total timeout
        start_time = datetime.now()

        while True:
            elapsed = (datetime.now() - start_time).total_seconds()
            if elapsed > timeout_seconds:
                print(f"[ERROR] Generation {generation_id} timed out after {elapsed:.1f} seconds")
                active_generations[generation_id]["status"] = "failed"
                active_generations[generation_id]["error"] = f"Generation timed out after {elapsed:.1f} seconds"
                break

            status_data = await comfyui_service.get_workflow_status(prompt_id)
            status = status_data.get("status")

            if status == "completed":
                print(f"[DEBUG] Execution completed successfully for prompt {prompt_id}")
                await handle_generation_complete(generation_id, prompt_id)
                break
            elif status == "failed":
                error_msg = status_data.get("error", "Unknown execution error")
                print(f"[ERROR] Execution error: {error_msg}")
                active_generations[generation_id]["status"] = "failed"
                active_generations[generation_id]["error"] = error_msg
                break
            
            # Update progress if available
            # This part needs to be adapted based on the data returned by get_workflow_status
            # For now, we'll just print the status
            print(f"[DEBUG] Generation {generation_id} status: {status}")


            await asyncio.sleep(2) # Poll every 2 seconds

    except Exception as e:
        error_msg = f"Error monitoring progress: {str(e)}"
        print(f"[ERROR] {error_msg}")
        active_generations[generation_id]["status"] = "failed"
        active_generations[generation_id]["error"] = error_msg

async def handle_generation_complete(generation_id: str, prompt_id: str):
    """Handle completed generation with actual image retrieval"""

    try:
        print(f"[DEBUG] Handling generation completion for {generation_id}")

        active_generations[generation_id]["status"] = "completed"
        active_generations[generation_id]["progress"] = 100
        active_generations[generation_id]["completed_at"] = datetime.now()

        # Try to get the actual generated image
        images = await comfyui_service.get_generated_images(prompt_id)
        
        if images:
            image_info = images[0]
            image_bytes = await comfyui_service.download_image(
                image_info["filename"], 
                image_info.get("subfolder", ""), 
                image_info.get("type", "output")
            )
            
            if image_bytes:
                image_data = base64.b64encode(image_bytes).decode('utf-8')
                file_ext = image_info["filename"].lower().split('.')[-1]
                mime_type = f'image/{file_ext}' if file_ext in ['jpeg', 'png', 'webp'] else 'image/png'
                active_generations[generation_id]["image_data"] = f"data:{mime_type};base64,{image_data}"
                print(f"[DEBUG] Successfully retrieved generated image for {generation_id}")
            else:
                raise Exception("Failed to download image data")
        else:
            # Fallback to placeholder if image retrieval fails
            placeholder_image_data = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            active_generations[generation_id]["image_data"] = placeholder_image_data
            print(f"[WARNING] Using placeholder image for {generation_id}")

        print(f"[SUCCESS] Generation {generation_id} completed successfully")

    except Exception as e:
        error_msg = f"Failed to process result: {str(e)}"
        print(f"[ERROR] {error_msg}")
        active_generations[generation_id]["status"] = "failed"
        active_generations[generation_id]["error"] = error_msg

@router.get("/generation/{generation_id}/status")
async def get_generation_status(generation_id: str):
    """Get generation status"""
    
    if generation_id not in active_generations:
        raise HTTPException(status_code=404, detail="Generation not found")
    
    generation = active_generations[generation_id]
    
    return {
        "generation_id": generation_id,
        "status": generation["status"],
        "progress": generation.get("progress", 0),
        "image_data": generation.get("image_data"),
        "error": generation.get("error"),
        "created_at": generation["created_at"].isoformat(),
        "completed_at": generation.get("completed_at", {}).isoformat() if generation.get("completed_at") else None
    }

@router.post("/generation/{generation_id}/cancel")
async def cancel_generation(generation_id: str):
    """Cancel generation"""
    
    if generation_id not in active_generations:
        raise HTTPException(status_code=404, detail="Generation not found")
    
    # Cancel in ComfyUI
    try:
        # This needs to be implemented in comfyui_service
        # await comfyui_service.cancel_workflow(active_generations[generation_id]["prompt_id"])
        pass
    except Exception as e:
        print(f"Failed to cancel in ComfyUI: {e}")
    
    # Update local status
    active_generations[generation_id]["status"] = "cancelled"
    
    return {"success": True, "message": "Generation cancelled"}

@router.get("/generation/active")
async def get_active_generations():
    """Get all active generations"""
    
    return {
        "active_generations": [
            {
                "generation_id": gen_id,
                "status": gen_data["status"],
                "progress": gen_data.get("progress", 0),
                "created_at": gen_data["created_at"].isoformat()
            }
            for gen_id, gen_data in active_generations.items()
            if gen_data["status"] in ["queued", "processing"]
        ]
    }

@router.get("/generation/nvidia-status")
async def get_nvidia_optimization_status():
    """Get NVIDIA optimization status and performance metrics"""
    
    try:
        # Try to get GPU info from system
        import subprocess
        import json as json_module
        
        try:
            # Try nvidia-smi to get GPU info
            result = subprocess.run([
                'nvidia-smi', 
                '--query-gpu=name,driver_version,memory.total,memory.used,temperature.gpu,utilization.gpu',
                '--format=csv,noheader,nounits'
            ], capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                gpu_info = result.stdout.strip().split(', ')
                return {
                    "nvidia_available": True,
                    "gpu_name": gpu_info[0] if len(gpu_info) > 0 else "Unknown",
                    "driver_version": gpu_info[1] if len(gpu_info) > 1 else "Unknown",
                    "vram_total_mb": int(gpu_info[2]) if len(gpu_info) > 2 and gpu_info[2].isdigit() else 0,
                    "vram_used_mb": int(gpu_info[3]) if len(gpu_info) > 3 and gpu_info[3].isdigit() else 0,
                    "temperature_c": int(gpu_info[4]) if len(gpu_info) > 4 and gpu_info[4].isdigit() else 0,
                    "utilization_percent": int(gpu_info[5]) if len(gpu_info) > 5 and gpu_info[5].isdigit() else 0,
                    "optimization_status": "optimal" if "RTX 4070 Ti SUPER" in gpu_info[0] else "suboptimal",
                    "recommendations": []
                }
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            pass
        
        # Fallback if nvidia-smi not available
        return {
            "nvidia_available": False,
            "message": "NVIDIA GPU information not available",
            "optimization_status": "unknown",
            "recommendations": [
                "Install NVIDIA drivers",
                "Ensure nvidia-smi is available in PATH",
                "Verify RTX 4070 Ti SUPER is properly installed"
            ]
        }
        
    except Exception as e:
        return {
            "nvidia_available": False,
            "error": str(e),
            "optimization_status": "error"
        }

@router.post("/cancel/{generation_id}")
async def cancel_generation(generation_id: str):
    """Cancel an active generation"""
    print(f"[DEBUG] Cancel request received for generation_id: {generation_id}")
    
    try:
        # Check if generation exists in our tracking
        if generation_id in active_generations:
            generation_data = active_generations[generation_id]
            prompt_id = generation_data.get("prompt_id")
            
            # Cancel in ComfyUI if we have a prompt_id
            if prompt_id:
                try:
                    await comfyui_service.cancel_generation(prompt_id)
                    print(f"[DEBUG] ComfyUI cancellation requested for prompt_id: {prompt_id}")
                except Exception as e:
                    print(f"[DEBUG] ComfyUI cancellation failed: {e}")
            
            # Remove from active generations
            del active_generations[generation_id]
            print(f"[DEBUG] Generation {generation_id} removed from active tracking")
        
        # Always try to cleanup ComfyUI queue/memory regardless
        try:
            await comfyui_service.clear_queue()
            await comfyui_service.free_memory()
            print(f"[DEBUG] ComfyUI queue cleared and memory freed")
        except Exception as e:
            print(f"[DEBUG] ComfyUI cleanup failed: {e}")
        
        return {
            "success": True,
            "message": f"Generation {generation_id} cancelled successfully",
            "generation_id": generation_id
        }
        
    except Exception as e:
        print(f"[ERROR] Cancel generation failed: {e}")
        # Still try to cleanup ComfyUI
        try:
            await comfyui_service.clear_queue()
            await comfyui_service.free_memory()
        except:
            pass
            
        return {
            "success": True,  # Return success even on errors to ensure frontend cleanup
            "message": f"Generation cancelled (forced cleanup)",
            "generation_id": generation_id,
            "warning": str(e)
        }
