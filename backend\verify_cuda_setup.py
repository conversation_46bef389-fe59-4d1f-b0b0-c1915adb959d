#!/usr/bin/env python3

import sys
import subprocess

def check_cuda_support():
    print("🔍 CUDA Support Verification")
    print("=" * 40)
    
    try:
        import torch
        print(f"✅ PyTorch installed: {torch.__version__}")
        print(f"✅ CUDA available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA version: {torch.version.cuda}")
            print(f"✅ GPU device count: {torch.cuda.device_count()}")
            
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                print(f"   GPU {i}: {gpu_name}")
        else:
            print("❌ CUDA not available")
            
    except ImportError:
        print("❌ PyTorch not installed")
        return False
    
    return torch.cuda.is_available()

def test_comfyui_connection():
    print(f"\n🌐 ComfyUI Connection Test")
    print("=" * 40)
    
    try:
        import requests
        response = requests.get("http://localhost:8188/", timeout=5)
        print(f"✅ ComfyUI accessible: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ ComfyUI not accessible: {e}")
        return False

def test_backend_connection():
    print(f"\n🔧 Backend Connection Test")
    print("=" * 40)
    
    try:
        import requests
        response = requests.get("http://localhost:8000/", timeout=5)
        print(f"✅ Backend accessible: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ Backend not accessible: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Complete System Verification")
    print("=" * 50)
    
    cuda_ok = check_cuda_support()
    comfyui_ok = test_comfyui_connection()
    backend_ok = test_backend_connection()
    
    print(f"\n📋 Summary:")
    print(f"   CUDA Support: {'✅' if cuda_ok else '❌'}")
    print(f"   ComfyUI Running: {'✅' if comfyui_ok else '❌'}")
    print(f"   Backend Running: {'✅' if backend_ok else '❌'}")
    
    if cuda_ok and comfyui_ok and backend_ok:
        print(f"\n🎉 All systems ready! You can test the generation fix.")
    else:
        print(f"\n⚠️  Some systems need attention.")
        
        if not cuda_ok:
            print(f"   📌 Fix CUDA: Run the start_with_cuda.bat script")
        if not comfyui_ok:
            print(f"   📌 Fix ComfyUI: Start ComfyUI manually or use start_with_cuda.bat")
        if not backend_ok:
            print(f"   📌 Fix Backend: Start backend server or use start_with_cuda.bat")
