#!/usr/bin/env python3
"""
Centralized Error Logging System for ComfyUI
Provides comprehensive error tracking with Claude accessibility
"""

import json
import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path

# Configuration
LOG_DIR = Path("C:/temp/comfyui_workspace/logs")
SYSTEM_ERRORS_FILE = LOG_DIR / "system_errors.json"
CLAUDE_ERRORS_FILE = LOG_DIR / "claude_errors.json"
ACTIVITY_LOG_FILE = LOG_DIR / "system_activity.log"

# Ensure log directory exists
LOG_DIR.mkdir(parents=True, exist_ok=True)

# Global session ID for tracking
SESSION_ID = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

class CentralizedLogger:
    def __init__(self):
        self.setup_logging()
        self.session_start_time = datetime.now()
        
    def setup_logging(self):
        """Setup Python logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(ACTIVITY_LOG_FILE),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_errors(self):
        """Load existing errors from JSON file"""
        try:
            if SYSTEM_ERRORS_FILE.exists():
                with open(SYSTEM_ERRORS_FILE, 'r') as f:
                    return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            pass
        
        return {
            "errors": [],
            "sessions": {},
            "metadata": {
                "created": datetime.now().isoformat(),
                "total_errors": 0
            }
        }
    
    def save_errors(self, error_data):
        """Save errors to JSON file"""
        try:
            with open(SYSTEM_ERRORS_FILE, 'w') as f:
                json.dump(error_data, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save error data: {e}")
    
    def update_claude_summary(self, error_data):
        """Update Claude-accessible error summary"""
        try:
            # Calculate summary statistics
            total_errors = len(error_data["errors"])
            critical_errors = len([e for e in error_data["errors"] if e["severity"] == "CRITICAL"])
            warnings = len([e for e in error_data["errors"] if e["severity"] == "WARNING"])
            
            # Get recent errors (last 10)
            recent_errors = error_data["errors"][-10:] if error_data["errors"] else []
            
            # Component status summary
            components = {}
            for error in error_data["errors"]:
                component = error["component"]
                if component not in components:
                    components[component] = {"errors": 0, "last_error": None}
                components[component]["errors"] += 1
                components[component]["last_error"] = error["timestamp"]
            
            claude_summary = {
                "last_updated": datetime.now().isoformat(),
                "session_id": SESSION_ID,
                "error_summary": {
                    "total_errors": total_errors,
                    "critical_errors": critical_errors,
                    "warnings": warnings,
                    "recent_errors": [
                        {
                            "timestamp": e["timestamp"],
                            "component": e["component"],
                            "severity": e["severity"],
                            "message": e["message"]
                        } for e in recent_errors
                    ]
                },
                "component_status": {
                    component: {
                        "error_count": data["errors"],
                        "last_error": data["last_error"],
                        "status": "ERROR" if data["errors"] > 0 else "OK"
                    } for component, data in components.items()
                }
            }
            
            with open(CLAUDE_ERRORS_FILE, 'w') as f:
                json.dump(claude_summary, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Failed to update Claude summary: {e}")
    
    def log_error(self, component, message, severity="ERROR"):
        """Log an error with specified severity"""
        timestamp = datetime.now().isoformat()
        
        error_entry = {
            "timestamp": timestamp,
            "session_id": SESSION_ID,
            "component": component,
            "severity": severity,
            "message": message
        }
        
        # Load existing errors
        error_data = self.load_errors()
        
        # Add new error
        error_data["errors"].append(error_entry)
        error_data["metadata"]["total_errors"] = len(error_data["errors"])
        error_data["metadata"]["last_updated"] = timestamp
        
        # Update session tracking
        if SESSION_ID not in error_data["sessions"]:
            error_data["sessions"][SESSION_ID] = {
                "start_time": self.session_start_time.isoformat(),
                "errors": 0
            }
        error_data["sessions"][SESSION_ID]["errors"] += 1
        
        # Save to files
        self.save_errors(error_data)
        self.update_claude_summary(error_data)
        
        # Log to activity log
        self.logger.log(
            getattr(logging, severity, logging.ERROR),
            f"[{component}] {message}"
        )
        
        return True
    
    def log_info(self, component, message):
        """Log informational message"""
        self.logger.info(f"[{component}] {message}")
        return True
    
    def log_warning(self, component, message):
        """Log warning message"""
        return self.log_error(component, message, "WARNING")
    
    def log_critical(self, component, message):
        """Log critical error message"""
        return self.log_error(component, message, "CRITICAL")
    
    def log_system_start(self, message):
        """Log system startup"""
        self.log_info("SYSTEM", f"System Start: {message}")
        return True

# Global logger instance
logger = CentralizedLogger()

def main():
    """Command line interface for logging"""
    if len(sys.argv) < 4:
        print("Usage: python centralized_logger.py <log_level> <component> <message>")
        print("Log levels: log_info, log_warning, log_error, log_critical, log_system_start")
        sys.exit(1)
    
    log_level = sys.argv[1]
    component = sys.argv[2]
    message = " ".join(sys.argv[3:])
    
    try:
        if log_level == "log_info":
            logger.log_info(component, message)
        elif log_level == "log_warning":
            logger.log_warning(component, message)
        elif log_level == "log_error":
            logger.log_error(component, message)
        elif log_level == "log_critical":
            logger.log_critical(component, message)
        elif log_level == "log_system_start":
            logger.log_system_start(message)
        else:
            print(f"Unknown log level: {log_level}")
            sys.exit(1)
            
        print(f"Logged: [{component}] {message}")
        
    except Exception as e:
        print(f"Logging failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
