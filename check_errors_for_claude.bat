@echo off
title Error Log Checker for <PERSON>
echo.
echo 📋 ComfyUI System Error Summary for <PERSON>
echo ==========================================
echo.

REM Check if log directory exists
if not exist "C:\temp\comfyui_workspace\logs" (
    echo ❌ Error: Log directory not found
    echo 💡 Logs should be at: C:\temp\comfyui_workspace\logs
    goto :end
)

cd /d "C:\temp\comfyui_workspace\logs"

echo 📍 Checking for recent errors...
echo.

REM Check if Claude error log exists
if exist "claude_errors.json" (
    echo ✅ Claude error log found
    echo.
    echo 📊 Recent Error Summary:
    echo ------------------------
    
    REM Display the Claude-accessible error summary
    type claude_errors.json
    
    echo.
    echo ========================
) else (
    echo ✅ No error log found - system appears clean
)

echo.
echo 📁 Available log files:
dir /b *.log *.json 2>nul || echo   No log files found

echo.
echo 📍 Log directory contents:
dir

echo.
echo 💡 To view full logs:
echo    - System errors: claude_errors.json
echo    - Activity log: system_activity.log
echo    - All errors: system_errors.json

:end
echo.
pause
