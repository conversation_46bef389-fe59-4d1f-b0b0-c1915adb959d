@echo off
title Port Checker - ComfyUI Services
echo.
echo 🔍 Checking ComfyUI Service Ports...
echo ========================================
echo.

REM Check port 8188 (ComfyUI)
echo 📍 Port 8188 (ComfyUI Server):
netstat -aon | findstr ":8188 " >nul 2>&1
if %errorlevel% == 0 (
    echo   ❌ OCCUPIED
    for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":8188 "') do (
        set "pid=%%a"
        for /f "tokens=*" %%b in ('tasklist /fi "pid eq %%a" /fo csv /nh 2^>nul') do (
            echo   🔗 Process: %%b
        )
    )
) else (
    echo   ✅ AVAILABLE
)

echo.
REM Check port 8000 (Backend API)
echo 📍 Port 8000 (Backend API):
netstat -aon | findstr ":8000 " >nul 2>&1
if %errorlevel% == 0 (
    echo   ❌ OCCUPIED
    for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":8000 "') do (
        set "pid=%%a"
        for /f "tokens=*" %%b in ('tasklist /fi "pid eq %%a" /fo csv /nh 2^>nul') do (
            echo   🔗 Process: %%b
        )
    )
) else (
    echo   ✅ AVAILABLE
)

echo.
REM Check port 3000 (Frontend)
echo 📍 Port 3000 (Frontend):
netstat -aon | findstr ":3000 " >nul 2>&1
if %errorlevel% == 0 (
    echo   ❌ OCCUPIED
    for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":3000 "') do (
        set "pid=%%a"
        for /f "tokens=*" %%b in ('tasklist /fi "pid eq %%a" /fo csv /nh 2^>nul') do (
            echo   🔗 Process: %%b
        )
    )
) else (
    echo   ✅ AVAILABLE
)

echo.
echo ========================================
echo 💡 If ports are occupied by old processes:
echo    1. Use STOP_ALL_SERVICES.bat to clean up
echo    2. Or manually kill the processes shown above
echo    3. Then try starting services again
echo.
pause
