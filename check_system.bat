@echo off
echo.
echo 🔍 Checking PyTorch and CUDA Setup
echo.

cd /d "g:\PORT_COMFY_front\ComfyUI_windows_portable"

echo 📍 Checking PyTorch version...
python_embeded\python.exe -c "import torch; print(f'PyTorch: {torch.__version__}')"

echo.
echo 📍 Checking CUDA availability...
python_embeded\python.exe -c "import torch; print(f'CUDA Available: {torch.cuda.is_available()}'); print(f'CUDA Device Count: {torch.cuda.device_count()}'); print(f'Current Device: {torch.cuda.current_device() if torch.cuda.is_available() else \"N/A\"}')"

echo.
echo 📍 Checking xformers status...
python_embeded\python.exe -c "try: import xformers; print(f'xformers: {xformers.__version__}'); except ImportError: print('xformers: Not installed (OK for stability)')"

echo.
echo 📍 GPU Memory Info...
python_embeded\python.exe -c "import torch; print(f'GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB') if torch.cuda.is_available() else print('No CUDA GPU detected')"

echo.
echo ✅ System check complete!
echo.
pause
