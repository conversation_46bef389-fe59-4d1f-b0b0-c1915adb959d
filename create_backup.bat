@echo off
echo.
echo 🔄 Creating Backup of ComfyUI Frontend Project
echo.

set BACKUP_DATE=%date:~-4,4%-%date:~-10,2%-%date:~-7,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%
set BACKUP_DATE=%BACKUP_DATE: =0%
set BACKUP_DIR=G:\backups\comfyui_front_%BACKUP_DATE%

echo 📂 Creating backup directory: %BACKUP_DIR%
mkdir "%BACKUP_DIR%" 2>nul

echo.
echo 📋 Copying project files...

echo   - Source code and configs...
xcopy "g:\comfyui_Front\*.md" "%BACKUP_DIR%\" /Y /Q
xcopy "g:\comfyui_Front\*.json" "%BACKUP_DIR%\" /Y /Q
xcopy "g:\comfyui_Front\*.bat" "%BACKUP_DIR%\" /Y /Q
xcopy "g:\comfyui_Front\*.ps1" "%BACKUP_DIR%\" /Y /Q
xcopy "g:\comfyui_Front\*.py" "%BACKUP_DIR%\" /Y /Q

echo   - Backend directory...
xcopy "g:\comfyui_Front\backend" "%BACKUP_DIR%\backend\" /E /I /Y /Q

echo   - Frontend directory...
xcopy "g:\comfyui_Front\frontend" "%BACKUP_DIR%\frontend\" /E /I /Y /Q

echo   - Documentation...
xcopy "g:\comfyui_Front\docs" "%BACKUP_DIR%\docs\" /E /I /Y /Q

echo   - Data directory...
xcopy "g:\comfyui_Front\data" "%BACKUP_DIR%\data\" /E /I /Y /Q

echo   - Source directory...
xcopy "g:\comfyui_Front\src" "%BACKUP_DIR%\src\" /E /I /Y /Q

echo   - Virtual environments (structure only)...
mkdir "%BACKUP_DIR%\Comfyvenv" 2>nul
copy "g:\comfyui_Front\Comfyvenv\pyvenv.cfg" "%BACKUP_DIR%\Comfyvenv\" 2>nul

echo.
echo 📝 Creating backup info file...
echo Backup Created: %date% %time% > "%BACKUP_DIR%\BACKUP_INFO.txt"
echo Source: g:\comfyui_Front >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo Status: System fully operational >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo Version: 1.1.0 - Major Stability Release >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo. >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo Major fixes included: >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo - Prompt ID not found errors - FIXED >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo - Frontend timeout issues - FIXED >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo - SQLAlchemy compatibility - FIXED >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo - PyTorch CUDA setup - OPTIMIZED >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo - Backend configuration - FIXED >> "%BACKUP_DIR%\BACKUP_INFO.txt"

echo.
echo ✅ Backup completed successfully!
echo.
echo 📍 Backup location: %BACKUP_DIR%
echo 📊 Use this backup to restore the working system if needed
echo.
pause
