@echo off
title Emergency Port Cleanup
color 0C
echo.
echo 🚨 EMERGENCY PORT CLEANUP
echo ========================
echo.
echo This will forcefully free ports 3000, 8000, and 8188
echo by terminating ANY processes using these ports.
echo.
echo ⚠️  WARNING: This may close other applications!
echo.

set /p confirm="Continue with emergency cleanup? (y/n): "
if /i not "%confirm%"=="y" (
    echo ❌ Operation cancelled.
    goto :end
)

echo.
echo 🛑 Emergency cleanup in progress...
echo.

REM Kill processes on port 8188 (ComfyUI)
echo 📍 Freeing port 8188 (ComfyUI)...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":8188 " 2^>nul') do (
    taskkill /f /pid %%a 2>nul
    echo   ✅ Killed process PID: %%a
)

REM Kill processes on port 8000 (Backend)
echo 📍 Freeing port 8000 (Backend)...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":8000 " 2^>nul') do (
    taskkill /f /pid %%a 2>nul
    echo   ✅ Killed process PID: %%a
)

REM Kill processes on port 3000 (Frontend)
echo 📍 Freeing port 3000 (Frontend)...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":3000 " 2^>nul') do (
    taskkill /f /pid %%a 2>nul
    echo   ✅ Killed process PID: %%a
)

echo.
echo 🧹 Additional cleanup...
REM Kill any remaining ComfyUI processes
taskkill /f /im python.exe 2>nul | findstr /i "comfyui" || echo   ℹ️  No ComfyUI Python processes found

REM Kill any remaining Node processes from frontend
taskkill /f /im node.exe 2>nul | findstr /i "frontend" || echo   ℹ️  No Frontend Node processes found

echo.
echo ✅ Emergency cleanup complete!
echo.
echo 📊 Checking port status:
timeout /t 2 /nobreak >nul

netstat -aon | findstr ":8188 " >nul 2>&1
if %errorlevel% == 0 (
    echo   ❌ Port 8188 still occupied
) else (
    echo   ✅ Port 8188 freed
)

netstat -aon | findstr ":8000 " >nul 2>&1
if %errorlevel% == 0 (
    echo   ❌ Port 8000 still occupied
) else (
    echo   ✅ Port 8000 freed
)

netstat -aon | findstr ":3000 " >nul 2>&1
if %errorlevel% == 0 (
    echo   ❌ Port 3000 still occupied
) else (
    echo   ✅ Port 3000 freed
)

echo.
echo 💡 You can now try starting ComfyUI services again.
echo.

:end
pause
