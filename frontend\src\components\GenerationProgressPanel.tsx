/**
 * GenerationProgressPanel Component
 * Real-time generation progress display with WebSocket integration
 */

import React, { useState } from 'react';
import { useGeneration } from '../hooks/useGeneration';
import { GenerationState, GenerationPayload } from '../services/generationApiService';

// Sample generation payload for testing
const samplePayload: GenerationPayload = {
  settings: {
    prompt: "A beautiful landscape with mountains and a lake, digital art, highly detailed",
    negative_prompt: "blurry, low quality, distorted",
    model: "SDXL-1.0",
    width: 1024,
    height: 1024,
    steps: 30,
    cfg_scale: 7.5,
    seed: -1,
    sampler: "DPM++ 2M Karras",
    scheduler: "normal"
  },
  hardware_config: {
    use_cpu: false,
    use_fp16: true,
    batch_size: 1,
    memory_management: "auto"
  },
  metadata: {
    session_id: `session_${Date.now()}`,
    tags: ["landscape", "digital-art", "test"],
    description: "Test generation from progress panel"
  }
};

interface GenerationProgressPanelProps {
  className?: string;
}

export const GenerationProgressPanel: React.FC<GenerationProgressPanelProps> = ({
  className
}) => {
  const [showHistory, setShowHistory] = useState(false);
  
  const {
    isGenerating,
    currentGeneration,
    progress,
    state,
    substage,
    message,
    error,
    result,
    startGeneration,
    cancelGeneration,
    clearResult,
    isWebSocketConnected,
    connectWebSocket,
    disconnectWebSocket,
    history,
    refreshHistory
  } = useGeneration({
    onProgress: (progressData) => {
      console.log('Generation progress:', progressData);
    },
    onComplete: (resultData) => {
      console.log('Generation complete:', resultData);
    },
    onError: (errorData) => {
      console.error('Generation error:', errorData);
    }
  });

  const getStateColor = (state: GenerationState | null) => {
    if (!state) return '#6b7280';
    
    switch (state) {
      case GenerationState.QUEUED:
        return '#3b82f6';
      case GenerationState.INITIALIZING:
        return '#8b5cf6';
      case GenerationState.PROCESSING:
        return '#8b5cf6';
      case GenerationState.RENDERING:
        return '#f59e0b';
      case GenerationState.COMPLETING:
        return '#f59e0b';
      case GenerationState.COMPLETED:
        return '#10b981';
      case GenerationState.FAILED:
        return '#ef4444';
      case GenerationState.CANCELLED:
        return '#f97316';
      default:
        return '#6b7280';
    }
  };

  const handleStartGeneration = async () => {
    try {
      clearResult();
      await startGeneration(samplePayload);
    } catch (err) {
      console.error('Failed to start generation:', err);
    }
  };

  const handleCancelGeneration = async () => {
    try {
      await cancelGeneration();
    } catch (err) {
      console.error('Failed to cancel generation:', err);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className || ''}`}>
      <h2 className="text-xl font-semibold mb-4">Generation Progress Monitor</h2>
      
      {/* WebSocket Status */}
      <div className="mb-4">
        <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
          isWebSocketConnected 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          <div className={`w-2 h-2 rounded-full mr-2 ${
            isWebSocketConnected ? 'bg-green-500' : 'bg-red-500'
          }`}></div>
          {isWebSocketConnected ? 'WebSocket Connected' : 'WebSocket Disconnected'}
        </div>
        {!isWebSocketConnected && (
          <button
            onClick={connectWebSocket}
            className="ml-2 px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Connect
          </button>
        )}
      </div>

      {/* Generation Status */}
      {state && (
        <div className="mb-4">
          <div 
            className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white"
            style={{ backgroundColor: getStateColor(state) }}
          >
            {state}{substage ? ` - ${substage}` : ''}
          </div>
        </div>
      )}

      {/* Progress Bar */}
      {isGenerating && (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-600">Progress: {progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Status Message */}
      {message && (
        <div className="mb-4">
          <p className="text-sm text-gray-600">{message}</p>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700">
          {error}
        </div>
      )}

      {/* Result Display */}
      {result && (
        <div className="mb-4">
          <div className="p-3 bg-green-100 border-l-4 border-green-500 text-green-700">
            Generation completed! Generated {result.images?.length || 0} image(s) in {result.processing_time?.toFixed(1)}s
          </div>
          {result.images && result.images.length > 0 && (
            <div className="mt-2">
              <p className="text-sm text-gray-600">
                Images: {result.images.map(img => `${img.dimensions.width}x${img.dimensions.height}`).join(', ')}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Control Buttons */}
      <div className="flex flex-wrap gap-2 mb-4">
        <button
          onClick={handleStartGeneration}
          disabled={isGenerating || !isWebSocketConnected}
          className={`px-4 py-2 rounded font-medium ${
            isGenerating || !isWebSocketConnected
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-500 text-white hover:bg-blue-600'
          }`}
        >
          ▶ Start Test Generation
        </button>
        
        {isGenerating && (
          <button
            onClick={handleCancelGeneration}
            className="px-4 py-2 bg-orange-500 text-white rounded font-medium hover:bg-orange-600"
          >
            ⏹ Cancel
          </button>
        )}
        
        <button
          onClick={refreshHistory}
          className="px-4 py-2 bg-gray-500 text-white rounded font-medium hover:bg-gray-600"
        >
          🔄 Refresh
        </button>
        
        <button
          onClick={() => setShowHistory(!showHistory)}
          className="px-4 py-2 bg-purple-500 text-white rounded font-medium hover:bg-purple-600"
        >
          📚 History ({history.length})
        </button>
        
        {(result || error) && (
          <button
            onClick={clearResult}
            className="px-4 py-2 bg-gray-500 text-white rounded font-medium hover:bg-gray-600"
          >
            Clear
          </button>
        )}
      </div>

      {/* Generation Details */}
      {currentGeneration && (
        <div className="mb-4">
          <p className="text-sm text-gray-500">
            Generation ID: {currentGeneration}
          </p>
        </div>
      )}

      {/* History */}
      {showHistory && (
        <div className="mt-4 border-t pt-4">
          <h3 className="text-lg font-medium mb-3">Recent Generations</h3>
          <div className="space-y-2">
            {history.slice(0, 5).map((gen) => (
              <div key={gen.generation_id} className="border rounded p-3 bg-gray-50">
                <div className="flex items-center gap-2 mb-1">
                  <div 
                    className="px-2 py-1 rounded text-xs font-medium text-white"
                    style={{ backgroundColor: getStateColor(gen.status as GenerationState) }}
                  >
                    {gen.status}
                  </div>
                  <span className="text-sm font-mono">
                    {gen.generation_id.substring(0, 8)}...
                  </span>
                </div>
                <p className="text-sm text-gray-600">
                  Progress: {gen.progress}% - {gen.message}
                </p>
                <p className="text-xs text-gray-500">
                  {new Date(gen.created_at).toLocaleString()}
                  {gen.processing_time && ` - ${gen.processing_time.toFixed(1)}s`}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default GenerationProgressPanel;
