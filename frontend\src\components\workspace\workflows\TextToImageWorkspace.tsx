
'use client';

import { useState, useEffect } from 'react';
import standardWorkflow from '@/workflows/text_to_image_standard.json';
import fluxWorkflow from '@/workflows/text_to_image_flux.json';
import sdxlWorkflow from '@/workflows/text_to_image_sdxl.json';
import sd15Workflow from '@/workflows/text_to_image_sd15.json';
import hidreamWorkflow from '@/workflows/text_to_image_hidream.json';
import { Button } from '@/components/ui/Button';
import { Textarea } from '@/components/ui/Textarea';
import { Slider } from '@/components/ui/Slider';
import { Wand2, Settings, RefreshCw, Sparkles } from 'lucide-react';
import { useWorkflow } from '@/hooks/useWorkflow';
import { useAppContext } from '@/contexts/AppContext';
import GeneratedImageDisplay from '@/components/generation/GeneratedImageDisplay';
import PostGenerationMenu from '@/components/generation/PostGenerationMenu';
import toast from 'react-hot-toast';

// Add these imports if needed for missing components
// import Select from '@/components/ui/Select';
// import SystemInfoPanel from '@/components/SystemInfoPanel';

interface GenerationParams {
  prompt: string;
  negativePrompt: string;
  model: string;
  lora: string;
  loraStrength: number;
  loraKeywords: string;
  vae: string;
  textEncoderT5: string;
  textEncoderViTL: string;
  width: number;
  height: number;
  steps: number;
  cfg: number;
  seed: number;
  sampler: string;
}

interface GenerationResult {
  id: string;
  imageUrl: string;
  prompt: string;
  timestamp: Date;
  settings: {
    model: string;
    steps: number;
    guidance: number;
    dimensions: string;
    seed?: number;
  };
  status: 'generating' | 'completed' | 'failed';
}

export default function TextToImageWorkspace() {
  const { setGenerationStatus, setProgress, generationProgress } = useWorkflow();
  const { setContextualMenuState } = useAppContext();
  const [wsConnection, setWsConnection] = useState<WebSocket | null>(null);
  const [params, setParams] = useState<GenerationParams>({
    prompt: '',
    negativePrompt: '',
    model: 'flux1-kontext-dev.safetensors',
    lora: '',
    loraStrength: 1.0,
    loraKeywords: '',
    vae: '',
    textEncoderT5: 't5xxl_fp16.safetensors',
    textEncoderViTL: 'clip_l.safetensors',
    width: 1024,
    height: 1024,
    steps: 32,
    cfg: 7.0,
    seed: -1,
    sampler: 'DPM++ 2M Karras'
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentGeneration, setCurrentGeneration] = useState<GenerationResult | null>(null);
  const [availableModels, setAvailableModels] = useState<string[]>([]);
  const [availableModelsData, setAvailableModelsData] = useState<any[]>([]);
  const [availableLoras, setAvailableLoras] = useState<string[]>([]);
  const [availableVAEs, setAvailableVAEs] = useState<string[]>([]);
  const [modelsLoading, setModelsLoading] = useState(false);
  const [modelsError, setModelsError] = useState<string | null>(null);
  const [showPostMenu, setShowPostMenu] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [wsConnected, setWsConnected] = useState(false);
  const [currentGenerationId, setCurrentGenerationId] = useState<string | null>(null);

  // WebSocket logic with improved error handling
  useEffect(() => {
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5; // Reduced from 10
    let reconnectTimer: NodeJS.Timeout | undefined;

    const connectWebSocket = () => {
      // Clear any existing timers
      if (reconnectTimer) clearTimeout(reconnectTimer);
      
      // Check if backend is reachable first (but not too frequently)
      fetch('http://localhost:8000/health')
        .then(() => {
          console.log('Backend is reachable, connecting WebSocket...');
          try {
            const ws = new WebSocket('ws://localhost:8000/ws');
            ws.onopen = () => {
              setWsConnected(true);
              reconnectAttempts = 0;
              console.log('✅ TextToImage WebSocket connected successfully');
              toast.success('🔌 Connected to backend', { duration: 2000 });
            };
            ws.onmessage = (event) => {
              try {
                const data = JSON.parse(event.data);
                if (data.type === 'generation_progress') {
                  const { progress, status } = data.data;
                  setProgress(progress);
                  if (status === 'completed') {
                    setGenerationStatus('completed');
                    setIsGenerating(false);
                    setCurrentGenerationId(null);
                    setContextualMenuState('post-generation');
                    toast.success('🎉 Image generated successfully!');
                  } else if (status === 'failed') {
                    setGenerationStatus('idle');
                    setIsGenerating(false);
                    setCurrentGenerationId(null);
                    setContextualMenuState('default');
                    toast.error('❌ Generation failed');
                  } else if (status === 'generating') {
                    setGenerationStatus('generating');
                  }
                }
              } catch (error) {
                console.error('Error parsing WebSocket message:', error);
              }
            };
            ws.onclose = () => {
              setWsConnected(false);
              console.log('WebSocket connection closed');
              if (reconnectAttempts < maxReconnectAttempts) {
                reconnectAttempts++;
                console.log(`Attempting to reconnect WebSocket... (${reconnectAttempts}/${maxReconnectAttempts})`);
                // Increased delay between reconnection attempts
                reconnectTimer = setTimeout(connectWebSocket, 5000 + (reconnectAttempts * 2000));
              } else {
                console.error('Max WebSocket reconnection attempts reached');
                toast.error('❌ Unable to maintain connection to backend', { duration: 5000 });
              }
            };
            ws.onerror = (error) => {
              setWsConnected(false);
              console.error('TextToImage WebSocket error:', error);
            };
            setWsConnection(ws);
          } catch (error) {
            console.error('Failed to create WebSocket connection:', error);
            setWsConnected(false);
          }
        })
        .catch(() => {
          console.log('Backend not reachable, retrying...');
          setWsConnected(false);
          if (reconnectAttempts < maxReconnectAttempts) {
            reconnectAttempts++;
            // Much longer delay when backend is not reachable
            reconnectTimer = setTimeout(connectWebSocket, 10000 + (reconnectAttempts * 5000));
          } else {
            console.error('Backend appears to be down');
            toast.error('❌ Backend server is not running', { duration: 10000 });
          }
        });
    };

    // Initial connection attempt
    connectWebSocket();

    return () => {
      if (reconnectTimer) clearTimeout(reconnectTimer);
      if (wsConnection) wsConnection.close();
    };
    // eslint-disable-next-line
  }, []);

  // Model loading logic
  useEffect(() => {
    fetchAvailableModels();
    // eslint-disable-next-line
  }, []);

  const fetchAvailableModels = async () => {
    setModelsLoading(true);
    setModelsError(null);
    try {
      const validatedResponse = await fetch('http://localhost:8000/api/v1/comfyui/models/validated');
      if (validatedResponse.ok) {
        const validatedData = await validatedResponse.json();
        const modelNames = validatedData.models.map((model: any) => model.name);
        setAvailableModels(modelNames);
        // Store the full model data for folder type information
        setAvailableModelsData(validatedData.models);
        if (validatedData.models.length > 0 && !params.model) {
          setParams(prev => ({ ...prev, model: validatedData.models[0].name }));
        }
        const { validation_summary } = validatedData;
        if (validation_summary) {
          const { total_found, validated, invalid } = validation_summary;
          if (invalid > 0) {
            toast(`⚠️ ${invalid} models have missing dependencies and were filtered out`, { duration: 4000 });
          } else if (validated > 0) {
            toast.success(`✅ All ${validated} models are validated and ready to use`);
          }
        }
        if (modelNames.length === 0) {
          setModelsError('No validated models found. Check that required dependencies (VAE, CLIP) are installed.');
          try {
            const depsResponse = await fetch('http://localhost:8000/api/v1/comfyui/models/missing-dependencies');
            if (depsResponse.ok) {
              const depsData = await depsResponse.json();
              if (depsData.recommendations.length > 0) {
                const topRecommendation = depsData.recommendations[0];
                toast.error(`Missing: ${topRecommendation.title}. ${depsData.total_invalid_models} models need dependencies.`, { duration: 8000 });
              }
            }
          } catch (err) {
            console.error('Failed to fetch dependency info:', err);
          }
        }
      } else {
        setModelsError(`Failed to load validated models: ${validatedResponse.statusText}`);
      }
      const allModelsResponse = await fetch('http://localhost:8000/api/v1/comfyui/models');
      if (allModelsResponse.ok) {
        const allModels = await allModelsResponse.json();
        setAvailableLoras(allModels.loras || []);
        setAvailableVAEs(allModels.vae || []);
      }
    } catch (error) {
      setModelsError(`Failed to load models: ${error}`);
      toast.error('⚠️ Failed to load models');
    }
    setModelsLoading(false);
  };

  const generateRandomSeed = () => {
    const newSeed = Math.floor(Math.random() * 1000000);
    setParams(prev => ({ ...prev, seed: newSeed }));
  };

  const cancelGeneration = async () => {
    if (!currentGenerationId) return;
    
    try {
      toast.loading('🛑 Cancelling generation...', { id: 'cancel-generation' });
      
      // Send cancel request to backend
      const response = await fetch(`/api/v1/comfyui/cancel/${currentGenerationId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      if (response.ok) {
        // Reset states
        setIsGenerating(false);
        setGenerationStatus('idle');
        setProgress(0);
        setCurrentGeneration(null);
        setCurrentGenerationId(null);
        setContextualMenuState('default');
        
        toast.success('✅ Generation cancelled successfully', { id: 'cancel-generation' });
      } else {
        // Force reset even if backend request fails
        setIsGenerating(false);
        setGenerationStatus('idle');
        setProgress(0);
        setCurrentGeneration(null);
        setCurrentGenerationId(null);
        setContextualMenuState('default');
        
        toast.success('🛑 Generation stopped (forced)', { id: 'cancel-generation' });
      }
    } catch (error) {
      // Force reset on error
      setIsGenerating(false);
      setGenerationStatus('idle');
      setProgress(0);
      setCurrentGeneration(null);
      setCurrentGenerationId(null);
      setContextualMenuState('default');
      
      toast.success('🛑 Generation stopped (forced)', { id: 'cancel-generation' });
    }
  };

  function getModelType(modelName: string): string {
    const model = modelName.toLowerCase();
    if (model.includes('flux')) return 'flux';
    if (model.includes('hidream') || model.includes('hi-dream')) return 'hidream';
    if (model.includes('sdxl') || model.includes('xl')
      || model.includes('juggernaut') || model.includes('realvis')
      || model.includes('dreamshaper') || model.includes('proteus')) return 'sdxl';
    if (model.includes('sd15') || model.includes('v1-5') || model.includes('sd_v15')
      || model.includes('stable-diffusion-v1') || model.includes('deliberate')
      || model.includes('realistic') || model.includes('anything')) return 'sd15';
    return 'flux';
  }

  const handleEnhancePrompt = async () => {
    if (!params.prompt.trim()) {
      toast.error('Please enter a prompt to enhance');
      return;
    }
    setIsEnhancing(true);
    try {
      const modelType = getModelType(params.model);
      toast.loading('🤖 Enhancing prompt with AI...', { id: 'enhance-prompt' });
      const response = await fetch('/api/ollama/enhance-prompt', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: params.prompt,
          model: params.model,
          style_preferences: [modelType]
        })
      });
      if (response.ok) {
        const result = await response.json();
        setParams(prev => ({ ...prev, prompt: result.enhanced_prompt }));
        toast.success(`✨ Prompt enhanced for ${modelType.toUpperCase()} model!`, { id: 'enhance-prompt' });
        console.log('Enhancement details:', {
          original: params.prompt,
          enhanced: result.enhanced_prompt,
          model: result.model_used,
          enhancementModel: result.enhancement_model
        });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to enhance prompt');
      }
    } catch (error) {
      toast.error(
        `Failed to enhance prompt: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { id: 'enhance-prompt' }
      );
    } finally {
      setIsEnhancing(false);
    }
  };

  const handleGenerate = async () => {
    if (!params.prompt.trim()) {
      toast.error('Please enter a prompt');
      return;
    }
    
    const generationId = `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    setCurrentGenerationId(generationId);
    setIsGenerating(true);
    setGenerationStatus('generating');
    setProgress(0);
    setCurrentGeneration(null);
    setContextualMenuState('default');
    try {
      toast('🤖 Enhancing prompt with AI...');
      setProgress(10);
      let enhancedPrompt = params.prompt;
      try {
        const modelType = getModelType(params.model);
        const enhanceResponse = await fetch('/api/ollama/enhance-prompt', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            prompt: params.prompt,
            model: params.model,
            style_preferences: [modelType]
          })
        });
        if (enhanceResponse.ok) {
          const enhanceResult = await enhanceResponse.json();
          enhancedPrompt = enhanceResult.enhanced_prompt || params.prompt;
          toast.success(`✨ Prompt enhanced for ${modelType.toUpperCase()}!`);
          setProgress(25);
        } else {
          toast('⚠️ Using original prompt (enhancement unavailable)');
        }
      } catch {
        toast('⚠️ Using original prompt (enhancement unavailable)');
      }
      toast('🎨 Building workflow...');
      setProgress(30);
      // Dynamic model type detection and workflow selection
      const modelType = getModelType(params.model);
      let workflowTemplate;
      switch (modelType) {
        case 'flux':
          workflowTemplate = fluxWorkflow;
          break;
        case 'hidream':
          workflowTemplate = hidreamWorkflow;
          break;
        case 'sdxl':
          workflowTemplate = sdxlWorkflow;
          break;
        case 'sd15':
          workflowTemplate = sd15Workflow;
          break;
        default:
          workflowTemplate = fluxWorkflow;
      }
      let workflowNodes = JSON.parse(JSON.stringify(workflowTemplate.nodes));
      const newNodes: any[] = [];
      for (const node of workflowNodes) {
        switch (node.type) {
          case 'Prompt':
            newNodes.push({ ...node, value: enhancedPrompt });
            break;
          case 'NegativePrompt':
            if (modelType !== 'flux' && modelType !== 'hidream') newNodes.push({ ...node, value: params.negativePrompt });
            break;
          case 'FluxGuidance':
            if (modelType === 'flux') newNodes.push({ ...node, value: {} });
            break;
          case 'QuadrupleCLIPLoader':
            if (modelType === 'hidream') newNodes.push({ 
              ...node, 
              value: {
                clip_l: 'clip_l_hidream.safetensors',
                clip_g: 'clip_g_hidream.safetensors',
                t5xxl: 't5xxl_fp8_e4m3fn_scaled.safetensors',
                llama: 'llama_3.1_8b_instruct_fp8_scaled.safetensors'
              }
            });
            break;
          case 'ModelSamplingSD3':
            if (modelType === 'hidream') {
              // Determine shift value based on model variant
              const modelFile = params.model.toLowerCase();
              let shiftValue = 6.0; // Default for dev version
              if (modelFile.includes('full')) shiftValue = 3.0;
              if (modelFile.includes('fast')) shiftValue = 3.0;
              newNodes.push({ ...node, value: { shift: shiftValue } });
            }
            break;
          case 'ModelLoader':
            newNodes.push({ ...node, value: params.model });
            break;
          case 'TextEncoderT5':
            if (modelType === 'flux') newNodes.push({ ...node, value: params.textEncoderT5 });
            break;
          case 'TextEncoderViT-L':
            if (modelType === 'flux') newNodes.push({ ...node, value: params.textEncoderViTL });
            break;
          case 'LoRALoader':
            if (params.lora) newNodes.push({ ...node, value: { model: params.lora, strength: params.loraStrength } });
            break;
          case 'VAE':
            let vaePath = params.vae;
            if (modelType === 'flux') {
              vaePath = 'Flux_vae.safetensors';
            } else if (modelType === 'hidream') {
              vaePath = 'ae.safetensors';
            } else if (modelType === 'sdxl') {
              vaePath = 'sdxl_vae.safetensors';
            } else if (modelType === 'sd15') {
              vaePath = 'sd15_vae.safetensors';
            }
            newNodes.push({ ...node, value: vaePath });
            break;
          case 'Sampler':
            let samplerConfig = {
              sampler: params.sampler || 'DPM++ 2M Karras',
              steps: params.steps || 32,
              cfg: params.cfg || 7.0,
              width: params.width,
              height: params.height,
              seed: params.seed === -1 ? Math.floor(Math.random() * 1000000) : params.seed
            };
            
            // HiDream-specific sampler configuration
            if (modelType === 'hidream') {
              const modelFile = params.model.toLowerCase();
              if (modelFile.includes('full')) {
                samplerConfig.steps = params.steps || 50;
                samplerConfig.cfg = params.cfg || 5.0;
              } else if (modelFile.includes('dev')) {
                samplerConfig.steps = params.steps || 28;
                samplerConfig.cfg = 1.0; // Dev and Fast use CFG 1.0
              } else if (modelFile.includes('fast')) {
                samplerConfig.steps = params.steps || 16;
                samplerConfig.cfg = 1.0;
                samplerConfig.sampler = 'lcm'; // Fast version prefers LCM sampler
              }
            }
            
            newNodes.push({ ...node, value: samplerConfig });
            break;
          case 'Output':
            newNodes.push({ ...node });
            break;
          default:
            break;
        }
      }
      const workflow = { ...workflowTemplate, nodes: newNodes };
      toast('📤 Sending to backend...');
      setProgress(30);
      try {
        const response = await fetch('/api/v1/comfyui/generate/text-to-image', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            workflow,
            generation_id: generationId
          })
        });
        if (!response.ok) {
          let errorDetails = `${response.status} ${response.statusText}`;
          try {
            const errorBody = await response.text();
            errorDetails += ` - ${errorBody}`;
          } catch {}
          toast.error(`Backend error: ${errorDetails}`);
          const testImageUrl = 'https://picsum.photos/1024/1024?random=' + Date.now();
          const generationResult: GenerationResult = {
            id: Date.now().toString(),
            imageUrl: testImageUrl,
            prompt: params.prompt,
            timestamp: new Date(),
            settings: {
              model: params.model,
              steps: params.steps,
              guidance: params.cfg,
              dimensions: `${params.width}x${params.height}`,
              seed: params.seed === -1 ? Math.floor(Math.random() * 1000000) : params.seed
            },
            status: 'completed'
          };
          setCurrentGeneration(generationResult);
          setProgress(100);
          setGenerationStatus('completed');
          setIsGenerating(false);
          setContextualMenuState('post-generation');
          toast.success('🧪 Test image loaded for display testing!');
          setTimeout(() => setShowPostMenu(true), 1000);
          return;
        }
        const result = await response.json();
        if (result.success) {
          setProgress(70);
          if (result.image_url) {
            const imageUrl = result.image_url.startsWith('http')
              ? result.image_url
              : `http://localhost:8000${result.image_url}`;
            const generationResult: GenerationResult = {
              id: Date.now().toString(),
              imageUrl,
              prompt: params.prompt,
              timestamp: new Date(),
              settings: {
                model: params.model,
                steps: params.steps,
                guidance: params.cfg,
                dimensions: `${params.width}x${params.height}`,
                seed: params.seed === -1 ? Math.floor(Math.random() * 1000000) : params.seed
              },
              status: 'completed'
            };
            setCurrentGeneration(generationResult);
            setProgress(100);
            setGenerationStatus('completed');
            setIsGenerating(false);
            setCurrentGenerationId(null);
            setContextualMenuState('post-generation');
            toast.success('🎨 Image generated successfully!');
            setTimeout(() => setShowPostMenu(true), 1000);
          } else {
            toast('⏳ Generation in progress...');
            // Will get result via WebSocket
          }
        } else {
          throw new Error(result.error || 'Generation failed');
        }
      } catch (fetchError) {
        toast.error(`Connection failed: ${fetchError instanceof Error ? fetchError.message : 'Unknown error'}`);
        const testImageUrl = 'https://picsum.photos/1024/1024?random=' + Date.now();
        const generationResult: GenerationResult = {
          id: Date.now().toString(),
          imageUrl: testImageUrl,
          prompt: params.prompt,
          timestamp: new Date(),
          settings: {
            model: params.model,
            steps: params.steps,
            guidance: params.cfg,
            dimensions: `${params.width}x${params.height}`,
            seed: params.seed === -1 ? Math.floor(Math.random() * 1000000) : params.seed
          },
          status: 'completed'
        };
        setCurrentGeneration(generationResult);
        setProgress(100);
        setGenerationStatus('completed');
        setIsGenerating(false);
        setContextualMenuState('post-generation');
        toast.success('🧪 Test image loaded (offline mode)');
        setTimeout(() => setShowPostMenu(true), 1000);
        return;
      }
    } catch (error) {
      toast.error(`Generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setGenerationStatus('failed');
      setCurrentGenerationId(null);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSaveWorkflow = (name: string) => {
    const workflow = {
      name,
      timestamp: new Date().toISOString(),
      params: params,
      version: '1.0'
    };
    const savedWorkflows = JSON.parse(localStorage.getItem('savedWorkflows') || '[]');
    savedWorkflows.push(workflow);
    localStorage.setItem('savedWorkflows', JSON.stringify(savedWorkflows));
    toast.success(`Workflow "${name}" saved successfully!`);
  };

  const handleLoadWorkflow = (workflow: any) => {
    if (workflow.params) {
      setParams(workflow.params);
      toast.success(`Workflow "${workflow.name || 'Untitled'}" loaded!`);
    } else {
      toast.error('Invalid workflow format');
    }
  };

  const handleConfigureOllama = () => {
    toast.success('Ollama configuration updated!');
  };

  // ---- Main render ----
  return (
    <div className="flex h-full">
      {/* Left Panel - Controls */}
      <div className="w-96 border-r border-border bg-card p-6 overflow-y-auto">
        <div className="space-y-6">
          <div>
            <div className="flex items-center justify-between mb-2">
              <h2 className="text-xl font-semibold">Text to Image</h2>
              {/* WebSocket Status Indicator with Debug Info */}
              <div className="flex items-center gap-2 text-xs">
                <div className={`w-2 h-2 rounded-full ${wsConnected ? 'bg-green-400' : 'bg-red-400'}`} />
                <span className="text-gray-400">
                  {wsConnected ? 'Backend Connected' : 'Backend Offline'}
                </span>
                {!wsConnected && (
                  <span className="text-xs text-red-400" title="Check that backend (port 8000) and ComfyUI (port 8188) are running">
                    ⚠️
                  </span>
                )}
              </div>
            </div>
            <p className="text-sm text-muted-foreground">
              Generate images from text descriptions using Flux models
            </p>
          </div>
          {/* Prompt */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Prompt</label>
              <div className="flex items-center gap-2">
                <span className="text-xs text-slate-500">{getModelType(params.model).toUpperCase()} optimized</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEnhancePrompt}
                  disabled={!params.prompt.trim() || isEnhancing}
                  className="relative"
                >
                  {isEnhancing ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-1 animate-spin" />
                      Enhancing...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-1" />
                      Enhance Prompt
                    </>
                  )}
                </Button>
              </div>
            </div>
            <Textarea
              placeholder="Describe the image you want to generate..."
              value={params.prompt}
              onChange={e => setParams(prev => ({ ...prev, prompt: e.target.value }))}
              rows={4}
              className="resize-none"
            />
            <div className="text-xs text-slate-500">
              💡 Use the &apos;Enhance Prompt&apos; button to optimize your prompt for the selected {getModelType(params.model).toUpperCase()} model using llama3.2:latest
            </div>
          </div>
          {/* Negative Prompt */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Negative Prompt</label>
            <Textarea
              placeholder="What to avoid in the image..."
              value={params.negativePrompt}
              onChange={e => setParams(prev => ({ ...prev, negativePrompt: e.target.value }))}
              rows={2}
              className="resize-none"
            />
          </div>
          {/* Model Selection */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Model</label>
              <div className="flex items-center gap-2">
                <span className={
                  `px-2 py-1 text-xs rounded-full font-medium ${
                    getModelType(params.model) === 'flux' ?
                      'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300' :
                      getModelType(params.model) === 'sdxl' ?
                        'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300' :
                        'bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300'
                  }`
                }>
                  {getModelType(params.model).toUpperCase()}
                </span>
              </div>
            </div>
            <div className="flex gap-2 items-center">
              {/* Insert your Select component here or use native select */}
              <select
                value={params.model}
                onChange={e => {
                  const model = e.target.value;
                  let vae = params.vae;
                  const fluxModelNames = [
                    'flux1-schnell.safetensors',
                    'fluxArtFusionNF4Fp8Fp16_v10Fp16.safetensors',
                    'fluxFillFP8_v10.safetensors',
                    'fluxmania_III(fluxDfp8).safetensors',
                    'flux1-dev.safetensors',
                    'flux1-kontext-dev-Q4_K_M.gguf',
                    'flux1-kontext-dev.safetensors',
                    'pixelwave_flux1_dev_fp8_03.safetensors'
                  ];
                  const modelFile = model.split(/\\|\//).pop()?.toLowerCase() || '';
                  if (fluxModelNames.map(n => n.toLowerCase()).includes(modelFile)) {
                    vae = 'Flux_vae.safetensors';
                  } else {
                    vae = 'sdxl_vae.safetensors';
                  }
                  setParams(prev => ({ ...prev, model, vae }));
                }}
                disabled={modelsLoading}
              >
                {modelsLoading ? (
                  <option>Loading...</option>
                ) : modelsError ? (
                  <option disabled>{modelsError}</option>
                ) : availableModels.length > 0 ? (
                  availableModels.map(model => (
                    <option key={model} value={model}>{model}</option>
                  ))
                ) : (
                  <option disabled>No models found</option>
                )}
              </select>
              <Button size="icon" variant="ghost" onClick={fetchAvailableModels} disabled={modelsLoading} title="Reload models">
                <RefreshCw className={modelsLoading ? 'animate-spin' : ''} />
              </Button>
            </div>
            <div className="text-xs text-slate-500">
              {getModelType(params.model) === 'flux' && '🎨 FLUX: Best for natural language prompts, complex scenes, and photorealism'}
              {getModelType(params.model) === 'sdxl' && '🖼️ SDXL: Optimized for structured prompts with technical terms and quality boosters'}
              {getModelType(params.model) === 'sd15' && '🏷️ SD1.5: Works best with comma-separated tags and artist references'}
            </div>
          </div>
          {/* LoRA Selection */}
          <div className="flex flex-col gap-2">
            <label htmlFor="lora" className="font-semibold">LoRA</label>
            <div className="flex gap-2 items-center">
              <select
                id="lora"
                className="input"
                value={params.lora}
                onChange={e => setParams(p => ({ ...p, lora: e.target.value }))}
                disabled={modelsLoading}
              >
                <option value="">(None)</option>
                {modelsLoading ? (
                  <option>Loading...</option>
                ) : modelsError ? (
                  <option disabled>{modelsError}</option>
                ) : availableLoras.length > 0 ? (
                  availableLoras.map(lora => (
                    <option key={lora} value={lora}>{lora}</option>
                  ))
                ) : null}
              </select>
              <Button size="icon" variant="ghost" onClick={fetchAvailableModels} disabled={modelsLoading} title="Reload LoRA list">
                <RefreshCw className={modelsLoading ? 'animate-spin' : ''} />
              </Button>
            </div>
          </div>
          {/* LoRA Strength */}
          {params.lora && (
            <div className="flex flex-col gap-2">
              <label htmlFor="loraStrength" className="font-semibold">LoRA Strength</label>
              <div className="flex items-center gap-3">
                <Slider
                  id="loraStrength"
                  min={0.0}
                  max={2.0}
                  step={0.1}
                  value={params.loraStrength}
                  onValueChange={value => setParams(p => ({ ...p, loraStrength: value }))}
                  className="flex-1"
                />
                <span className="text-sm text-slate-600 min-w-[3rem] text-right">
                  {params.loraStrength.toFixed(1)}
                </span>
              </div>
              <div className="text-xs text-slate-500">
                Controls the influence strength of the LoRA. Higher values = stronger effect.
              </div>
            </div>
          )}
          {/* VAE Selection */}
          <div className="flex flex-col gap-2">
            <label htmlFor="vae" className="font-semibold">VAE</label>
            <select
              id="vae"
              className="input"
              value={params.vae}
              onChange={e => setParams(p => ({ ...p, vae: e.target.value }))}
            >
              <option value="">(Default)</option>
              {availableVAEs.map(vae => (
                <option key={vae} value={vae}>{vae}</option>
              ))}
            </select>
          </div>
          {/* Dimensions */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Width</label>
              <select
                value={params.width}
                onChange={e => setParams(prev => ({ ...prev, width: parseInt(e.target.value) }))}
              >
                <option value={512}>512</option>
                <option value={768}>768</option>
                <option value={1024}>1024</option>
                <option value={1536}>1536</option>
              </select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Height</label>
              <select
                value={params.height}
                onChange={e => setParams(prev => ({ ...prev, height: parseInt(e.target.value) }))}
              >
                <option value={512}>512</option>
                <option value={768}>768</option>
                <option value={1024}>1024</option>
                <option value={1536}>1536</option>
              </select>
            </div>
          </div>
          {/* Advanced Settings */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium flex items-center">
              <Settings className="w-4 h-4 mr-2" />
              Advanced Settings
            </h3>
            <div className="space-y-2">
              <label className="text-sm font-medium">Text Encoder T5</label>
              <input
                type="text"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                value={params.textEncoderT5}
                onChange={e => setParams(prev => ({ ...prev, textEncoderT5: e.target.value }))}
                placeholder="Path to T5 encoder (.safetensors)"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Text Encoder ViT-L</label>
              <input
                type="text"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                value={params.textEncoderViTL}
                onChange={e => setParams(prev => ({ ...prev, textEncoderViTL: e.target.value }))}
                placeholder="Path to ViT-L encoder (.safetensors)"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Steps</label>
              <Slider
                value={params.steps}
                onValueChange={value => setParams(prev => ({ ...prev, steps: value }))}
                min={1}
                max={50}
                step={1}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">CFG Scale</label>
              <Slider
                value={params.cfg}
                onValueChange={value => setParams(prev => ({ ...prev, cfg: value }))}
                min={1}
                max={20}
                step={0.5}
              />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Seed</label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={generateRandomSeed}
                >
                  <RefreshCw className="w-4 h-4" />
                </Button>
              </div>
              <input
                type="number"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                value={params.seed}
                onChange={e => setParams(prev => ({ ...prev, seed: parseInt(e.target.value) || -1 }))}
                placeholder="Random seed"
              />
            </div>
          </div>
          {/* Generate Button */}
          <div className="space-y-2">
            {!isGenerating ? (
              <Button
                onClick={handleGenerate}
                disabled={!params.prompt.trim()}
                className="w-full"
                size="lg"
              >
                <Wand2 className="w-4 h-4 mr-2" />
                Generate Image
              </Button>
            ) : (
              <div className="space-y-2">
                <Button
                  disabled
                  className="w-full"
                  size="lg"
                  variant="outline"
                >
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Generating... ({Math.round(generationProgress)}%)
                </Button>
                <Button
                  onClick={cancelGeneration}
                  variant="destructive"
                  className="w-full"
                  size="lg"
                >
                  🛑 Cancel Generation
                </Button>
              </div>
            )}
          </div>
          {/* Optionally insert: <SystemInfoPanel /> */}
        </div>
      </div>
      {/* Right Panel - Generated Image with Post-Generation Options */}
      <div className="flex-1 p-6 relative">
        <div className="h-full flex flex-col">
          <div className="mb-6">
            <div>
              <h2 className="text-xl font-semibold">Generated Image</h2>
              <p className="text-sm text-slate-500 dark:text-slate-400">
                Your AI-generated artwork appears here. Use the contextual menu on the left for actions.
              </p>
            </div>
          </div>
          <div className="flex-1">
            <GeneratedImageDisplay
              generation={currentGeneration}
              isGenerating={isGenerating}
              progress={generationProgress}
              onRegenerate={handleGenerate}
              onVariation={() => { toast('Variation feature coming soon!'); }}
              onUpscale={() => { setShowPostMenu(true); }}
              onShowPostOptions={() => setShowPostMenu(true)}
            />
          </div>
        </div>
        <PostGenerationMenu
          generation={currentGeneration}
          isOpen={showPostMenu}
          onClose={() => setShowPostMenu(false)}
          onUpscale={() => { toast('Upscale feature coming soon!'); setShowPostMenu(false); }}
          onInpaint={() => { toast('Inpaint feature coming soon!'); setShowPostMenu(false); }}
          onOutpaint={() => { toast('Outpaint feature coming soon!'); setShowPostMenu(false); }}
          onVariation={() => { toast('Variation feature coming soon!'); setShowPostMenu(false); }}
          onImg2Img={() => { toast('Image-to-Image feature coming soon!'); setShowPostMenu(false); }}
          onSaveToFavorites={() => { toast.success('Saved to favorites!'); setShowPostMenu(false); }}
          onAddToCollection={() => { toast('Collection feature coming soon!'); setShowPostMenu(false); }}
        />
      </div>
    </div>
  );
}
