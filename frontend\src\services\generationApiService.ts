/**
 * Generation API Service
 * Handles text-to-image generation requests with real-time progress updates via WebSocket
 */

import { websocketManager } from './websocketManager';

export interface GenerationSettings {
  prompt: string;
  negative_prompt?: string;
  model: string;
  width: number;
  height: number;
  steps: number;
  cfg_scale: number;
  seed?: number;
  sampler?: string;
  scheduler?: string;
}

export interface HardwareConfig {
  use_cpu: boolean;
  use_fp16: boolean;
  batch_size: number;
  memory_management: string;
}

export interface GenerationMetadata {
  session_id?: string;
  user_id?: string;
  tags?: string[];
  description?: string;
}

export interface GenerationPayload {
  settings: GenerationSettings;
  hardware_config: HardwareConfig;
  metadata: GenerationMetadata;
}

export interface GenerationResponse {
  success: boolean;
  generation_id: string;
  message: string;
  queue_position?: number;
  estimated_time?: number;
}

export interface GenerationProgress {
  generation_id: string;
  state: GenerationState;
  progress: number;
  substage?: string;
  message?: string;
  error?: string;
  results?: any;
  metadata?: any;
  timestamp: string;
}

export interface GenerationStatus {
  generation_id: string;
  status: GenerationState;
  stage: string;
  substage: string;
  progress: number;
  message?: string;
  created_at: string;
  processing_time?: number;
  queue_position?: number;
  estimated_time?: number;
  error_count: number;
  retry_count: number;
  state_history: Array<{
    state: string;
    timestamp: string;
    message: string;
    progress?: number;
    substage?: string;
  }>;
}

export enum GenerationState {
  QUEUED = 'QUEUED',
  INITIALIZING = 'INITIALIZING',
  PROCESSING = 'PROCESSING',
  RENDERING = 'RENDERING',
  COMPLETING = 'COMPLETING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

export interface GenerationImage {
  id: string;
  data: string; // base64 encoded
  format: string;
  dimensions: {
    width: number;
    height: number;
  };
  file_size: number;
  created_at: string;
}

export interface GenerationResult {
  generation_id: string;
  status: GenerationState;
  images: GenerationImage[];
  processing_time: number;
  metadata: any;
}

export type GenerationProgressCallback = (progress: GenerationProgress) => void;
export type GenerationCompleteCallback = (result: GenerationResult) => void;
export type GenerationErrorCallback = (error: { generation_id: string; error: string; error_code?: string; details?: any }) => void;

class GenerationApiService {
  private baseUrl: string;
  private activeGenerations: Map<string, {
    progressCallback?: GenerationProgressCallback;
    completeCallback?: GenerationCompleteCallback;
    errorCallback?: GenerationErrorCallback;
  }> = new Map();

  constructor(baseUrl: string = 'http://localhost:8000/api/v1') {
    this.baseUrl = baseUrl;
    this.initializeWebSocketHandlers();
  }

  /**
   * Initialize WebSocket handlers for real-time updates
   */
  private initializeWebSocketHandlers() {
    websocketManager.onMessage((message) => {
      this.handleWebSocketMessage(message);
    });
  }

  /**
   * Handle incoming WebSocket messages for generation updates
   */
  private handleWebSocketMessage(message: any) {
    const { type, data } = message;

    switch (type) {
      case 'generation_progress':
        this.handleGenerationProgress(data);
        break;
      case 'generation_state':
        this.handleGenerationState(data);
        break;
      case 'generation_error':
        this.handleGenerationError(data);
        break;
      case 'generation_complete':
        this.handleGenerationComplete(data);
        break;
      default:
        // Ignore unknown message types
        break;
    }
  }

  /**
   * Handle generation progress updates
   */
  private handleGenerationProgress(progressData: GenerationProgress) {
    const callbacks = this.activeGenerations.get(progressData.generation_id);
    if (callbacks?.progressCallback) {
      callbacks.progressCallback(progressData);
    }
  }

  /**
   * Handle generation state changes
   */
  private handleGenerationState(stateData: any) {
    const progressData: GenerationProgress = {
      generation_id: stateData.generation_id,
      state: stateData.state,
      progress: stateData.data.progress || 0,
      substage: stateData.data.substage,
      message: stateData.data.message,
      timestamp: stateData.timestamp,
      metadata: stateData.data
    };

    this.handleGenerationProgress(progressData);
  }

  /**
   * Handle generation errors
   */
  private handleGenerationError(errorData: any) {
    const callbacks = this.activeGenerations.get(errorData.generation_id);
    if (callbacks?.errorCallback) {
      callbacks.errorCallback({
        generation_id: errorData.generation_id,
        error: errorData.error,
        error_code: errorData.error_code,
        details: errorData.details
      });
    }
  }

  /**
   * Handle generation completion
   */
  private handleGenerationComplete(completeData: any) {
    const callbacks = this.activeGenerations.get(completeData.generation_id);
    if (callbacks?.completeCallback) {
      // Fetch the final result to get images
      this.getGenerationResult(completeData.generation_id)
        .then(result => {
          if (callbacks.completeCallback) {
            callbacks.completeCallback(result);
          }
        })
        .catch(error => {
          console.error('Failed to fetch generation result:', error);
          if (callbacks.errorCallback) {
            callbacks.errorCallback({
              generation_id: completeData.generation_id,
              error: 'Failed to fetch generation result',
              details: { originalError: error }
            });
          }
        });
    }

    // Clean up callbacks
    this.activeGenerations.delete(completeData.generation_id);
  }

  /**
   * Start a new text-to-image generation
   */
  async startGeneration(
    payload: GenerationPayload,
    progressCallback?: GenerationProgressCallback,
    completeCallback?: GenerationCompleteCallback,
    errorCallback?: GenerationErrorCallback
  ): Promise<GenerationResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: GenerationResponse = await response.json();

      // Store callbacks for real-time updates
      if (result.success && result.generation_id) {
        this.activeGenerations.set(result.generation_id, {
          progressCallback,
          completeCallback,
          errorCallback
        });
      }

      return result;
    } catch (error) {
      console.error('Failed to start generation:', error);
      throw error;
    }
  }

  /**
   * Get generation status
   */
  async getGenerationStatus(generationId: string): Promise<GenerationStatus> {
    try {
      const response = await fetch(`${this.baseUrl}/generation/${generationId}/status`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to get generation status:', error);
      throw error;
    }
  }

  /**
   * Get generation result with images
   */
  async getGenerationResult(generationId: string): Promise<GenerationResult> {
    try {
      const response = await fetch(`${this.baseUrl}/generation/${generationId}/result`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to get generation result:', error);
      throw error;
    }
  }

  /**
   * Cancel a generation
   */
  async cancelGeneration(generationId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/generation/${generationId}/cancel`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      // Clean up callbacks
      this.activeGenerations.delete(generationId);

      return result;
    } catch (error) {
      console.error('Failed to cancel generation:', error);
      throw error;
    }
  }

  /**
   * Get generation history
   */
  async getGenerationHistory(limit: number = 10): Promise<GenerationStatus[]> {
    try {
      const response = await fetch(`${this.baseUrl}/generation/history?limit=${limit}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to get generation history:', error);
      throw error;
    }
  }

  /**
   * Get generation statistics
   */
  async getGenerationStats(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/generation/stats`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to get generation statistics:', error);
      throw error;
    }
  }

  /**
   * Register callbacks for an existing generation
   */
  registerCallbacks(
    generationId: string,
    progressCallback?: GenerationProgressCallback,
    completeCallback?: GenerationCompleteCallback,
    errorCallback?: GenerationErrorCallback
  ): void {
    this.activeGenerations.set(generationId, {
      progressCallback,
      completeCallback,
      errorCallback
    });
  }

  /**
   * Unregister callbacks for a generation
   */
  unregisterCallbacks(generationId: string): void {
    this.activeGenerations.delete(generationId);
  }

  /**
   * Check if WebSocket is connected
   */
  isWebSocketConnected(): boolean {
    return websocketManager.isConnected();
  }

  /**
   * Connect to WebSocket if not already connected
   */
  async connectWebSocket(): Promise<void> {
    if (!this.isWebSocketConnected()) {
      await websocketManager.connect();
    }
  }

  /**
   * Disconnect WebSocket
   */
  disconnectWebSocket(): void {
    websocketManager.disconnect();
  }
}

// Export singleton instance
export const generationApiService = new GenerationApiService();
export default GenerationApiService;
