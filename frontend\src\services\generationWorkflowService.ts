/**
 * Enhanced Generation Workflow Service
 * Handles the complete end-to-end text-to-image generation process with intelligent model validation
 */

import { GenerationSettings, ModelInfo, ComfyUIWorkflow } from './comfyUIService';
import { generationSchemaService, GenerationPayload } from './generationSchemaService';
import { websocketManager } from './websocketManager';
import { modelRegistryService, EnhancedModelInfo, ValidationResult } from './optimizedModelRegistryService';
import { workflowFactoryRegistry, WorkflowFactory } from './workflowFactoryService';
import { advancedWorkflowBuilder, WorkflowGenerationRequest } from './advancedWorkflowBuilder';
import { workflowValidationService } from './workflowValidationService';
import { workflowDataService } from './workflowDataService';

export interface GenerationStep {
  id: string;
  name: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  message: string;
  timestamp: number;
  duration?: number;
  data?: any;
}

interface GenerationMetadata {
  model: string;
  prompt: string;
  settings: GenerationSettings;
  processingTime: number;
  startTime?: Date;
  queuePosition?: number;
  factory: string;
  modelType: string;
  corrections?: string[];
}

export interface GenerationResult {
  success: boolean;
  generationId: string;
  imageData?: string; // Base64 encoded image
  metadata?: GenerationMetadata;
  error?: string;
  steps: GenerationStep[];
  validation?: ValidationResult;
  request?: any; // Store the original request for analysis
}

export interface GenerationProgress {
  generationId: string;
  currentStep: string;
  progress: number; // 0-100
  queuePosition?: number;
  estimatedTimeRemaining?: number;
  message: string;
}

interface PerformanceData {
  totalGenerations: number;
  totalTime: number;
  averageTime: number;
  successRate: number;
  commonErrors: Map<string, number>;
  lastGeneration: Date;
}

class EnhancedGenerationWorkflowService {
  private activeGenerations = new Map<string, GenerationResult>();
  private progressCallbacks = new Map<string, (progress: GenerationProgress) => void>();
  private performanceTracker = new Map<string, PerformanceData>();

  /**
   * Start enhanced generation workflow with dynamic workflow building
   */
  async startGenerationWithDynamicWorkflow(
    settings: GenerationSettings,
    modelInfo: ModelInfo,
    mode: 'txt2img' | 'img2img' | 'inpainting' | 'outpainting' | 'controlnet' | 'upscaling' | 'img2vid' = 'txt2img',
    onProgress?: (progress: GenerationProgress) => void
  ): Promise<GenerationResult> {
    const generationId = `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    console.log(`[GENERATION] Starting dynamic workflow generation ${generationId} for mode: ${mode}`);
    console.log(`[GENERATION] Model: ${settings.model}, Mode: ${mode}`);

    const result: GenerationResult = {
      success: false,
      generationId,
      steps: [],
      metadata: {
        model: settings.model,
        prompt: settings.prompt,
        settings,
        processingTime: 0,
        startTime: new Date(startTime),
        factory: 'dynamic_workflow_builder',
        modelType: 'detecting...'
      }
    };

    this.activeGenerations.set(generationId, result);

    if (onProgress) {
      this.progressCallbacks.set(generationId, onProgress);
    }

    try {
      // Step 1: Build dynamic workflow with validation
      const workflow = await this.executeStep(generationId, 'dynamic_workflow_build', 'Building optimized workflow', async () => {
        const workflowRequest: WorkflowGenerationRequest = {
          mode,
          model: settings.model,
          modelType: undefined, // Let the builder detect it
          prompt: settings.prompt,
          negative_prompt: settings.negative_prompt,
          width: settings.width,
          height: settings.height,
          steps: settings.steps,
          cfg_scale: settings.cfg_scale,
          sampler: settings.sampler,
          scheduler: settings.scheduler,
          seed: settings.seed,
          batch_size: settings.batch_size,
          lora: settings.lora,
          lora_strength: settings.lora_strength,
          vae: settings.vae,
          denoise_strength: settings.denoise_strength,
          prioritize_speed: settings.memory_management?.aggressive_unloading,
          prioritize_quality: !settings.memory_management?.aggressive_unloading,
          memory_optimization: settings.memory_management?.aggressive_unloading ? 'high' : 'medium'
        };

        const buildResult = await advancedWorkflowBuilder.generateWorkflow(workflowRequest);
        
        // Update metadata with detected model type
        if (result.metadata) {
          const modelType = advancedWorkflowBuilder['detectModelType'](settings.model);
          result.metadata.modelType = modelType;
        }

        // Store validation result and start tracking
        result.validation = buildResult.validationResult;
        
        // Start workflow data tracking
        const trackingId = await workflowDataService.startTracking(
          workflowRequest,
          buildResult.workflow,
          buildResult.validationResult,
          settings
        );

        return { 
          workflow: buildResult.workflow,
          validationResult: buildResult.validationResult,
          nodeCount: Object.keys(buildResult.workflow).length,
          workflowRequest,
          trackingId
        };
      });

      // Step 2: Validate dynamic workflow (enhanced)
      await this.executeStep(generationId, 'workflow_validation', 'Validating generated workflow', async () => {
        const workflowData = result.steps.find(s => s.id === 'dynamic_workflow_build')?.data;
        if (!workflowData?.workflow) {
          throw new Error('Failed to generate workflow');
        }

        const validationResult = workflowData.validationResult;
        
        // Check for fatal errors
        const fatalErrors = validationResult.errors.filter(e => e.fatal);
        if (fatalErrors.length > 0) {
          throw new Error(`Workflow validation failed with ${fatalErrors.length} fatal error(s): ${fatalErrors[0].message}`);
        }

        // Log validation summary
        const nodeCount = Object.keys(workflowData.workflow).length;
        const summary = {
          nodeCount,
          corrections: validationResult.corrections.length,
          warnings: validationResult.warnings.length,
          errors: validationResult.errors.length,
          isValid: validationResult.isValid
        };

        console.log(`[GENERATION] Workflow validation summary:`, summary);

        if (validationResult.corrections.length > 0) {
          console.log(`[GENERATION] Auto-corrected ${validationResult.corrections.length} parameters`);
        }

        return { 
          validated: true, 
          validationSummary: summary,
          trackingId: workflowData.trackingId
        };
      });

      // Step 3: Send dynamic workflow to ComfyUI
      await this.executeStep(generationId, 'comfyui_submission', 'Sending dynamic workflow to ComfyUI', async () => {
        const workflowData = result.steps.find(s => s.id === 'dynamic_workflow_build')?.data;
        const validationData = result.steps.find(s => s.id === 'workflow_validation')?.data;
        
        // Update tracking with execution start
        if (validationData?.trackingId) {
          await workflowDataService.updateExecutionStatus(validationData.trackingId, 'processing', {
            executionStartTime: Date.now()
          });
        }
        
        const response = await this.sendWorkflowToComfyUI(workflowData.workflow);
        return { promptId: response.prompt_id, response, trackingId: validationData?.trackingId };
      });

      // Step 4: Monitor ComfyUI processing
      await this.executeStep(generationId, 'comfyui_processing', 'Processing with ComfyUI', async () => {
        const submissionData = result.steps.find(s => s.id === 'comfyui_submission')?.data;
        const comfyResult = await this.monitorComfyUIProcessing(generationId, { prompt_id: submissionData.promptId });
        
        // Update tracking with completion status
        if (submissionData?.trackingId) {
          await workflowDataService.updateExecutionStatus(submissionData.trackingId, 'completed', {
            processingTime: comfyResult.processingTime,
            memoryUsage: comfyResult.memoryUsage
          });
        }
        
        return { comfyResult, trackingId: submissionData?.trackingId };
      });

      // Step 5: Process results
      await this.executeStep(generationId, 'result_processing', 'Processing generation results', async () => {
        const processingData = result.steps.find(s => s.id === 'comfyui_processing')?.data;
        const imageData = await this.processGenerationResults(processingData.comfyResult);
        result.imageData = imageData;
        
        // Complete tracking with final results
        if (processingData?.trackingId) {
          await workflowDataService.completeTracking(processingData.trackingId, {
            success: !!imageData,
            imageGenerated: !!imageData,
            imageSize: imageData ? new Blob([imageData]).size : undefined,
            processingTime: result.metadata!.processingTime
          });
        }
        
        return { imageData: imageData ? 'Generated' : 'Failed', trackingId: processingData?.trackingId };
      });

      // Mark as successful
      result.success = true;
      result.metadata!.processingTime = Date.now() - startTime;

      console.log(`[GENERATION] Dynamic workflow generation ${generationId} completed successfully in ${result.metadata!.processingTime}ms`);

    } catch (error) {
      console.error(`[GENERATION] Dynamic workflow generation ${generationId} failed:`, error);
      result.error = error instanceof Error ? error.message : String(error);
      result.success = false;

      // Handle tracking for failed generation
      const lastStepData = result.steps[result.steps.length - 1]?.data;
      if (lastStepData?.trackingId) {
        try {
          await workflowDataService.completeTracking(lastStepData.trackingId, {
            success: false,
            error: result.error,
            errorDetails: error instanceof Error ? { stack: error.stack, name: error.name } : error
          });
        } catch (trackingError) {
          console.warn('[GENERATION] Failed to update tracking for failed generation:', trackingError);
        }
      }

      await this.executeStep(generationId, 'error_handling', 'Handling generation error', async () => {
        return { error: result.error };
      });
    } finally {
      // Cleanup
      this.progressCallbacks.delete(generationId);
      
      // Keep the result for a while for debugging
      setTimeout(() => {
        this.activeGenerations.delete(generationId);
      }, 300000); // 5 minutes
    }

    return result;
  }

  /**
   * Start the complete generation workflow with intelligent model handling (legacy method)
   */
  async startGeneration(
    settings: GenerationSettings,
    modelInfo: ModelInfo,
    onProgress?: (progress: GenerationProgress) => void
  ): Promise<GenerationResult> {
    const generationId = `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    // Enhanced logging for generation start
    console.log(`[GENERATION] Starting image generation ${generationId} at ${new Date().toISOString()}`);
    console.log(`[GENERATION] Settings for ${generationId}:`, {
      model: settings.model,
      prompt: settings.prompt.substring(0, 100) + (settings.prompt.length > 100 ? '...' : ''),
      dimensions: `${settings.width}x${settings.height}`,
      steps: settings.steps,
      cfgScale: settings.cfg_scale,
      batchSize: settings.batch_size,
      seed: settings.seed
    });
    console.log(`[GENERATION] Model info for ${generationId}:`, {
      name: modelInfo.name,
      type: modelInfo.type,
      size: modelInfo.size
    });

    // Initialize generation result
    const result: GenerationResult = {
      success: false,
      generationId,
      steps: [],
      metadata: {
        model: settings.model,
        prompt: settings.prompt,
        settings,
        processingTime: 0,
        startTime: new Date(startTime),
        factory: 'default',
        modelType: 'unknown'
      },
      request: { settings, modelInfo } // Store original request
    };

    this.activeGenerations.set(generationId, result);

    if (onProgress) {
      this.progressCallbacks.set(generationId, onProgress);
    }

    try {
      // Step 1: Validate settings
      await this.executeStep(generationId, 'validation', 'Validating generation settings', async () => {
        const validation = generationSchemaService.validateSettings(settings, modelInfo);
        if (!validation.isValid) {
          throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
        }
        return { validation };
      });

      // Step 2: Prepare payload
      await this.executeStep(generationId, 'payload_preparation', 'Preparing generation payload', async () => {
        const optimizedSettings = generationSchemaService.validateSettings(settings, modelInfo).optimizedSettings!;
        const payload = generationSchemaService.createGenerationPayload(optimizedSettings);
        return { payload };
      });

      // Step 3: Send to backend
      await this.executeStep(generationId, 'backend_request', 'Sending request to backend', async () => {
        const payload = result.steps.find(s => s.id === 'payload_preparation')?.data?.payload;
        const response = await this.sendToBackend(payload);
        return { backendResponse: response };
      });

      // Step 4: Monitor ComfyUI processing
      await this.executeStep(generationId, 'comfyui_processing', 'Processing with ComfyUI', async () => {
        const backendResponse = result.steps.find(s => s.id === 'backend_request')?.data?.backendResponse;
        const comfyResult = await this.monitorComfyUIProcessing(generationId, backendResponse);
        return { comfyResult };
      });

      // Step 5: Receive and process result
      await this.executeStep(generationId, 'result_processing', 'Processing generated image', async () => {
        const comfyResult = result.steps.find(s => s.id === 'comfyui_processing')?.data?.comfyResult;
        const processedResult = await this.processGenerationResult(comfyResult);
        
        result.imageData = processedResult.imageData;
        result.success = true;
        
        return { processedResult };
      });

      // Calculate total processing time
      const startTime = result.steps[0]?.timestamp || Date.now();
      const endTime = Date.now();
      result.metadata!.processingTime = endTime - startTime;

      // Step 6: Add to history store
      if (result.success && result.imageData) {
        try {
          const { useHistoryStore } = await import('@/stores/historyStore');
          const { addGeneration } = useHistoryStore.getState();
          
          addGeneration({
            id: generationId,
            prompt: result.metadata!.prompt,
            negative_prompt: result.metadata!.settings.negative_prompt || '',
            image: result.imageData,
            thumbnail: result.imageData, // Use same image as thumbnail for now
            workflow: result.request,
            metadata: {
              model: result.metadata!.model,
              sampler: result.metadata!.settings.sampler,
              steps: result.metadata!.settings.steps,
              cfg_scale: result.metadata!.settings.cfg_scale,
              width: result.metadata!.settings.width,
              height: result.metadata!.settings.height,
              seed: result.metadata!.settings.seed,
              prompt: result.metadata!.prompt,
              negative_prompt: result.metadata!.settings.negative_prompt || '',
              workflow_type: 'text2img' as const,
              generation_time: result.metadata!.processingTime
            },
            status: 'completed',
            favorite: false
          });
          
          console.log(`[SUCCESS] Added generation ${generationId} to history`);
        } catch (error) {
          console.error('Failed to add generation to history:', error);
        }
      }

      return result;

    } catch (error) {
      console.error('Generation workflow failed:', error);
      
      // Mark as failed
      result.success = false;
      result.error = error instanceof Error ? error.message : 'Unknown error';
      
      // Add failure step
      result.steps.push({
        id: 'failure',
        name: 'Generation Failed',
        status: 'failed',
        message: result.error,
        timestamp: Date.now()
      });

      return result;
    } finally {
      // Cleanup
      this.progressCallbacks.delete(generationId);
    }
  }

  /**
   * Execute a workflow step with progress tracking
   */
  private async executeStep(
    generationId: string,
    stepId: string,
    stepName: string,
    executor: () => Promise<any>
  ): Promise<void> {
    const result = this.activeGenerations.get(generationId)!;
    const startTime = Date.now();

    // Enhanced logging for step execution
    console.log(`[GENERATION] Starting step "${stepId}" (${stepName}) for ${generationId} at ${new Date().toISOString()}`);

    // Add step to result
    const step: GenerationStep = {
      id: stepId,
      name: stepName,
      status: 'in_progress',
      message: `${stepName}...`,
      timestamp: startTime
    };

    result.steps.push(step);

    // Notify progress with percentage
    const progressPercent = this.calculateProgress(result.steps.length);
    this.notifyProgress(generationId, stepName, progressPercent);
    console.log(`[GENERATION] Progress for ${generationId}: ${progressPercent}% - ${stepName}`);

    try {
      // Execute step with timing
      console.log(`[GENERATION] Executing step "${stepId}" for ${generationId}...`);
      const stepData = await executor();
      const duration = Date.now() - startTime;

      // Mark as completed
      step.status = 'completed';
      step.message = `${stepName} completed`;
      step.duration = duration;
      step.data = stepData;

      // Performance logging
      console.log(`[GENERATION] Step "${stepId}" completed for ${generationId} in ${duration}ms`);

      // Log slow operations (>2s as requested)
      if (duration > 2000) {
        console.warn(`[PERFORMANCE] Slow operation detected: Step "${stepId}" took ${duration}ms for ${generationId}`);
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Mark as failed
      step.status = 'failed';
      step.message = `${stepName} failed: ${errorMessage}`;
      step.duration = duration;

      // Enhanced error logging with context
      console.error(`[GENERATION] Step "${stepId}" failed for ${generationId} after ${duration}ms:`, {
        error: errorMessage,
        stack: error instanceof Error ? error.stack : undefined,
        stepId: stepId,
        stepName: stepName,
        generationId: generationId,
        duration: duration
      });

      throw error;
    }
  }

  /**
   * Send generation request to backend with comprehensive logging
   */
  /**
   * Send workflow directly to ComfyUI without backend middleware
   */
  private async sendWorkflowToComfyUI(workflow: ComfyUIWorkflow): Promise<any> {
    const requestStartTime = Date.now();
    const endpoint = 'http://localhost:8188/prompt';

    const payload = {
      prompt: workflow,
      client_id: `frontend_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };

    console.log(`[COMFYUI] Sending workflow directly to ComfyUI at ${endpoint}`);
    console.log(`[COMFYUI] Workflow nodes:`, Object.keys(workflow).length);
    console.log(`[COMFYUI] Workflow details:`, JSON.stringify(workflow, null, 2));

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      const requestTime = Date.now() - requestStartTime;
      console.log(`[COMFYUI] Direct request completed in ${requestTime}ms with status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[COMFYUI] Direct request failed:`, {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`ComfyUI request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      console.log(`[COMFYUI] Direct request successful:`, result);

      return result;
    } catch (error) {
      const requestTime = Date.now() - requestStartTime;
      console.error(`[COMFYUI] Direct request error after ${requestTime}ms:`, error);
      throw error;
    }
  }

  private async sendToBackend(payload: GenerationPayload): Promise<any> {
    const requestStartTime = Date.now();
    const endpoint = 'http://localhost:8000/api/v1/generation/start';

    // Transform payload to match backend expectations for /api/v1/generation/start
    const backendPayload = {
      settings: {
        prompt: payload.settings.prompt,
        negative_prompt: payload.settings.negative_prompt || "",
        model: payload.settings.model,
        sampler: payload.settings.sampler || "euler",
        steps: payload.settings.steps || 20,
        cfg_scale: payload.settings.cfg_scale || 7.0,
        width: payload.settings.width || 1024,
        height: payload.settings.height || 1024,
        seed: payload.settings.seed || -1
      },
      workflow_type: "text_to_image",
      hardware_config: {
        vram_gb: 16,
        gpu_model: "RTX 4070 Ti SUPER",
        optimization_level: "balanced"
      },
      metadata: {
        frontend_version: "1.0.0",
        generation_timestamp: Date.now(),
        user_agent: navigator.userAgent || "ComfyUI-Frontend"
      }
    };

    console.log(`[BACKEND] Sending request to ${endpoint} at ${new Date().toISOString()}`);
    console.log(`[BACKEND] Original payload:`, JSON.stringify(payload, null, 2));
    console.log(`[BACKEND] Transformed backend payload:`, JSON.stringify(backendPayload, null, 2));

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(backendPayload)
      });

      const requestTime = Date.now() - requestStartTime;
      console.log(`[BACKEND] Request completed in ${requestTime}ms with status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[BACKEND] Request failed:`, {
          status: response.status,
          statusText: response.statusText,
          errorBody: errorText,
          endpoint: endpoint,
          requestTime: requestTime,
          sentPayload: backendPayload
        });
        throw new Error(`Backend request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const responseData = await response.json();
      console.log(`[BACKEND] Raw response received:`, JSON.stringify(responseData, null, 2));

      // Log all available response keys for debugging
      console.log(`[BACKEND] Response structure analysis:`, {
        availableKeys: Object.keys(responseData),
        hasPromptId: 'prompt_id' in responseData,
        hasId: 'id' in responseData,
        hasData: 'data' in responseData,
        hasResult: 'result' in responseData,
        responseType: typeof responseData
      });

      return responseData;

    } catch (error) {
      const requestTime = Date.now() - requestStartTime;
      console.error(`[BACKEND] Request failed after ${requestTime}ms:`, {
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
        endpoint: endpoint,
        originalPayload: payload,
        sentPayload: backendPayload
      });
      throw error;
    }
  }

  /**
   * Monitor ComfyUI processing via backend polling with enhanced debugging
   */
  private async monitorComfyUIProcessing(generationId: string, backendResponse: any): Promise<any> {
    const startTime = Date.now();
    const timeoutMs = 300000; // 5 minutes
    const pollInterval = 2000; // 2 seconds

    console.log(`[DEBUG] Starting ComfyUI monitoring for generation ${generationId}`);
    console.log(`[DEBUG] Backend response:`, JSON.stringify(backendResponse, null, 2));

    // Comprehensive prompt_id extraction with multiple fallback paths
    console.log(`[PROMPT_ID] Starting extraction from backend response...`);
    console.log(`[PROMPT_ID] Response type: ${typeof backendResponse}, keys: [${Object.keys(backendResponse || {})}]`);

    // Primary extraction attempt
    let promptId = backendResponse?.prompt_id;
    let extractionPath = 'backendResponse.prompt_id';

    // Comprehensive fallback extraction chain
    if (!promptId) {
      const extractionAttempts = [
        { path: 'backendResponse.data.prompt_id', value: backendResponse?.data?.prompt_id },
        { path: 'backendResponse.result.prompt_id', value: backendResponse?.result?.prompt_id },
        { path: 'backendResponse.response.prompt_id', value: backendResponse?.response?.prompt_id },
        { path: 'backendResponse.id', value: backendResponse?.id },
        { path: 'backendResponse.promptId', value: backendResponse?.promptId },
        { path: 'backendResponse.generation_id', value: backendResponse?.generation_id },
        { path: 'backendResponse.comfyui_id', value: backendResponse?.comfyui_id },
        { path: 'backendResponse.prompt_uuid', value: backendResponse?.prompt_uuid },
        { path: 'backendResponse.comfyui.prompt_id', value: backendResponse?.comfyui?.prompt_id }
      ];

      console.log(`[PROMPT_ID] Primary extraction failed, trying ${extractionAttempts.length} fallback methods...`);

      for (const attempt of extractionAttempts) {
        console.log(`[PROMPT_ID] Trying ${attempt.path}: ${attempt.value}`);
        if (attempt.value !== undefined && attempt.value !== null && attempt.value !== '') {
          promptId = attempt.value;
          extractionPath = attempt.path;
          console.log(`[PROMPT_ID] ✅ Successfully extracted from ${attempt.path}: ${promptId}`);
          break;
        }
      }
    } else {
      console.log(`[PROMPT_ID] ✅ Primary extraction successful: ${promptId}`);
    }

    // Final validation
    if (!promptId || promptId === '' || promptId === null || promptId === undefined) {
      const errorDetails = {
        extractionPath: extractionPath,
        extractedValue: promptId,
        responseKeys: Object.keys(backendResponse || {}),
        responseType: typeof backendResponse,
        fullResponse: backendResponse
      };

      console.error(`[PROMPT_ID] ❌ All extraction methods failed:`, errorDetails);

      const errorMsg = `Failed to extract prompt_id from backend response. Tried all extraction paths. Response keys: [${Object.keys(backendResponse || {})}]`;
      throw new Error(errorMsg);
    }

    // Validate prompt_id format
    const promptIdStr = String(promptId);
    if (promptIdStr.length === 0) {
      throw new Error(`Extracted prompt_id is empty string: "${promptId}"`);
    }

    console.log(`[PROMPT_ID] ✅ Final validation passed. Using prompt_id: "${promptId}" (extracted from: ${extractionPath})`);

    return new Promise(async (resolve, reject) => {
      const pollStatus = async () => {
        try {
          const elapsed = Date.now() - startTime;

          // Check for timeout
          if (elapsed > timeoutMs) {
            console.error(`[ERROR] Generation ${generationId} timed out after ${elapsed}ms`);
            reject(new Error(`Generation timed out after ${Math.round(elapsed / 1000)} seconds`));
            return;
          }

          // Poll backend for status using correct endpoint
          const response = await fetch(`http://localhost:8000/api/v1/comfyui/status/${promptId}`);

          if (!response.ok) {
            console.error(`[ERROR] Failed to get status for ${generationId}: ${response.statusText}`);
            setTimeout(pollStatus, pollInterval);
            return;
          }

          const statusData = await response.json();
          console.log(`[DEBUG] Status update for ${generationId}:`, statusData);

          // Parse the backend response format
          const comfyStatus = statusData.status; // This comes from our enhanced backend

          // Calculate progress based on status
          let progress = 50; // Default processing progress
          if (comfyStatus === 'queued') {
            progress = 20;
            this.notifyProgress(generationId, 'Queued for processing', progress);
          } else if (comfyStatus === 'processing') {
            progress = Math.min(80, 50 + (elapsed / timeoutMs) * 30); // Gradually increase to 80%
            this.notifyProgress(generationId, 'Processing with ComfyUI', progress);
          } else if (comfyStatus === 'completed') {
            progress = 100;
            this.notifyProgress(generationId, 'Generation completed', progress);
          }

          // Check completion
          if (comfyStatus === 'completed') {
            console.log(`[SUCCESS] Generation ${generationId} completed successfully`);
            resolve(statusData);
            return;
          }

          // Check for failure
          if (comfyStatus === 'failed' || comfyStatus === 'error') {
            const error = statusData.error || 'Generation failed without specific error';
            console.error(`[ERROR] Generation ${generationId} failed: ${error}`);
            reject(new Error(error));
            return;
          }

          // Continue polling if still processing
          if (comfyStatus === 'processing' || comfyStatus === 'queued') {
            setTimeout(pollStatus, pollInterval);
          } else if (comfyStatus === 'unknown') {
            // Handle unknown status - might be a new prompt that hasn't started yet
            console.log(`[DEBUG] Unknown status for ${generationId}, continuing to poll...`);
            setTimeout(pollStatus, pollInterval);
          } else {
            console.warn(`[WARNING] Unexpected status for ${generationId}: ${comfyStatus}`);
            setTimeout(pollStatus, pollInterval);
          }

        } catch (error) {
          console.error(`[ERROR] Error polling status for ${generationId}:`, error);

          // Don't fail immediately on network errors, retry
          const elapsed = Date.now() - startTime;
          if (elapsed < timeoutMs) {
            setTimeout(pollStatus, pollInterval * 2); // Longer interval on error
          } else {
            reject(new Error(`Network error during monitoring: ${error instanceof Error ? error.message : 'Unknown error'}`));
          }
        }
      };

      // Start polling
      setTimeout(pollStatus, 1000); // Initial delay
    });
  }

  /**
   * Process the final generation result
   */
  /**
   * Process generation results from direct ComfyUI workflow execution
   */
  private async processGenerationResults(comfyResult: any): Promise<string | null> {
    console.log(`[GENERATION] Processing ComfyUI results:`, comfyResult);
    
    try {
      // Handle different result formats from ComfyUI direct API
      if (comfyResult?.imageData) {
        // Standard format with base64 image data
        return comfyResult.imageData;
      } else if (comfyResult?.images && comfyResult.images.length > 0) {
        // Array of images format
        return comfyResult.images[0];
      } else if (comfyResult?.outputs) {
        // Node outputs format - look for SaveImage outputs
        for (const [nodeId, output] of Object.entries(comfyResult.outputs)) {
          if (output && Array.isArray(output) && output.length > 0) {
            const imageOutput = output[0];
            if (imageOutput?.image || imageOutput?.data) {
              return imageOutput.image || imageOutput.data;
            }
          }
        }
      } else if (typeof comfyResult === 'string' && comfyResult.startsWith('data:image/')) {
        // Direct base64 string
        return comfyResult;
      }
      
      console.warn(`[GENERATION] No image data found in ComfyUI result format:`, Object.keys(comfyResult || {}));
      return null;
    } catch (error) {
      console.error(`[GENERATION] Error processing ComfyUI results:`, error);
      return null;
    }
  }

  private async processGenerationResult(comfyResult: any): Promise<{ imageData: string }> {
    // Process the image data from ComfyUI
    if (!comfyResult.imageData) {
      throw new Error('No image data received from ComfyUI');
    }

    // Validate image data
    if (!comfyResult.imageData.startsWith('data:image/')) {
      throw new Error('Invalid image data format');
    }

    return {
      imageData: comfyResult.imageData
    };
  }

  /**
   * Calculate progress percentage based on completed steps
   */
  private calculateProgress(completedSteps: number): number {
    const totalSteps = 5; // validation, payload, backend, comfyui, result
    return Math.min(100, (completedSteps / totalSteps) * 100);
  }

  /**
   * Notify progress to callback
   */
  private notifyProgress(generationId: string, currentStep: string, progress: number): void {
    const callback = this.progressCallbacks.get(generationId);
    if (callback) {
      callback({
        generationId,
        currentStep,
        progress,
        message: `${currentStep} (${Math.round(progress)}%)`
      });
    }
  }

  /**
   * Get generation status
   */
  getGenerationStatus(generationId: string): GenerationResult | null {
    return this.activeGenerations.get(generationId) || null;
  }

  /**
   * Cancel generation
   */
  async cancelGeneration(generationId: string): Promise<void> {
    const result = this.activeGenerations.get(generationId);
    if (result) {
      // Send cancel request to backend
      try {
        await fetch(`http://localhost:8000/api/v1/generate/${generationId}/cancel`, {
          method: 'POST'
        });
      } catch (error) {
        console.warn('Failed to cancel generation on backend:', error);
      }

      // Mark as cancelled locally
      result.success = false;
      result.error = 'Generation cancelled by user';
      
      // Cleanup
      this.activeGenerations.delete(generationId);
      this.progressCallbacks.delete(generationId);
    }
  }

  /**
   * Get all active generations
   */
  getActiveGenerations(): GenerationResult[] {
    return Array.from(this.activeGenerations.values());
  }
}

export const generationWorkflowService = new EnhancedGenerationWorkflowService();
export default EnhancedGenerationWorkflowService;
