@echo off
echo.
echo 🚀 Git Repository Initialization Script
echo.
echo This script will:
echo 1. Initialize a Git repository
echo 2. Add all project files (respecting .gitignore)
echo 3. Create initial commit
echo 4. Prepare for GitHub push
echo.

set /p confirm="Do you want to continue? (y/n): "
if /i not "%confirm%"=="y" (
    echo Operation cancelled.
    goto end
)

echo.
echo 📍 Checking if Git is installed...
git --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Git is not installed or not in PATH
    echo Please install Git from: https://git-scm.com/
    goto end
)

echo ✅ Git is available
echo.

echo 📍 Initializing Git repository...
cd /d "g:\comfyui_Front"
git init

echo.
echo 📍 Adding files to Git (respecting .gitignore)...
git add .

echo.
echo 📍 Creating initial commit...
git commit -m "Initial commit - ComfyUI Frontend v1.1.0

✅ FULLY OPERATIONAL SYSTEM
- Fixed prompt ID not found errors
- Implemented async generation with polling
- Resolved SQLAlchemy 2.0 compatibility
- Optimized PyTorch 2.8.0+cu128 with CUDA 12.8
- Fixed backend configuration validation
- Added multiple startup options

System ready for production use with RTX 4070 Ti SUPER."

echo.
echo 📍 Setting up default branch...
git branch -M main

echo.
echo ✅ Git repository initialized successfully!
echo.
echo 🔗 Next steps for GitHub:
echo.
echo 1. Create a new repository on GitHub
echo 2. Copy the repository URL
echo 3. Run this command to add remote:
echo    git remote add origin [YOUR_GITHUB_REPO_URL]
echo.
echo 4. Push to GitHub:
echo    git push -u origin main
echo.
echo 📊 Repository status:
git status

:end
echo.
pause
