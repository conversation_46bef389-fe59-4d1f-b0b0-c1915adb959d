# ComfyUI Modular Monitoring System

A completely isolated, modular monitoring system that tracks your ComfyUI application stack for discrepancies and issues.

## Quick Start

1. **Install Node.js** (if not already installed)
2. **Install dependencies:**
   ```bash
   cd monitoring-system
   npm install
   ```
3. **Run the monitor:**
   ```bash
   node start.js
   ```

## Modular Architecture

### Current Modules
- **HttpHealthModule** - HTTP service health monitoring
- **WebSocketModule** - WebSocket connection monitoring
- **AlertModule** - Alert management and file logging

### What It Monitors

**HTTP Services:**
- ComfyUI (port 8188) - Core image generation service
- FastAPI Backend (port 8000) - Your backend API
- Next.js Frontend (port 3003) - Web interface
- Ollama (port 11434) - LLM service

**WebSocket Connections:**
- FastAPI Backend WebSocket (ws://127.0.0.1:8000/ws)
- ComfyUI WebSocket (ws://127.0.0.1:8188/ws)

**Alert Severity Levels:**
- 🔴 **CRITICAL** - Service completely down, WebSocket errors
- 🟠 **HIGH** - Service errors, WebSocket disconnections
- 🟡 **MEDIUM** - Performance issues (slow responses)
- 🔵 **LOW** - Informational messages, recoveries

## Output

### Console Logging
Real-time colored console output with timestamps and severity levels.

### File Logging
All alerts saved to `monitoring.log` for historical analysis.

### Web Dashboard
Simple web interface at http://localhost:9000 showing:
- Current service status
- Recent alerts
- Auto-refreshing every 5 seconds

## Configuration

Edit the `SERVICES` object in `simple-monitor.js` to modify:
- Service URLs
- Health check endpoints
- Monitoring intervals
- Alert thresholds

## Example Output

```
[LOW] 2024-01-15T10:30:00.000Z - Starting ComfyUI Monitoring System...
[LOW] 2024-01-15T10:30:00.100Z - Dashboard available at http://localhost:9000
[LOW] 2024-01-15T10:30:01.000Z - Running health check...
[LOW] 2024-01-15T10:30:02.500Z - All services healthy
[CRITICAL] 2024-01-15T10:35:01.000Z - ComfyUI connection failed: ECONNREFUSED
[HIGH] 2024-01-15T10:35:01.100Z - 1 services have issues
```

## Next Steps

This basic monitor can be extended with:
- WebSocket connection monitoring
- API response validation
- Email/webhook alerts
- Historical data storage
- Performance metrics
- Custom health checks

The system runs completely independently and won't interfere with your main application.
