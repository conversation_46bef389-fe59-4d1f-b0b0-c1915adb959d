# ComfyUI Independent Monitoring System

A completely isolated monitoring system that tracks your ComfyUI application stack for discrepancies and issues.

## Quick Start

1. **Install Node.js** (if not already installed)
2. **Run the monitor:**
   ```bash
   cd monitoring-system
   node simple-monitor.js
   ```
3. **View dashboard:** Open http://localhost:9000

## What It Monitors

### Services Monitored
- **ComfyUI** (port 8188) - Core image generation service
- **FastAPI Backend** (port 8000) - Your backend API
- **Next.js Frontend** (port 3003) - Web interface
- **Ollama** (port 11434) - LLM service

### Alert Severity Levels
- 🔴 **CRITICAL** - Service completely down
- 🟠 **HIGH** - Service errors or failures  
- 🟡 **MEDIUM** - Performance issues (slow responses)
- 🔵 **LOW** - Informational messages

### Detection Capabilities
- Service availability (up/down status)
- Response time monitoring
- HTTP status code validation
- Connection timeout detection
- Service recovery notifications

## Output

### Console Logging
Real-time colored console output with timestamps and severity levels.

### File Logging
All alerts saved to `monitoring.log` for historical analysis.

### Web Dashboard
Simple web interface at http://localhost:9000 showing:
- Current service status
- Recent alerts
- Auto-refreshing every 5 seconds

## Configuration

Edit the `SERVICES` object in `simple-monitor.js` to modify:
- Service URLs
- Health check endpoints
- Monitoring intervals
- Alert thresholds

## Example Output

```
[LOW] 2024-01-15T10:30:00.000Z - Starting ComfyUI Monitoring System...
[LOW] 2024-01-15T10:30:00.100Z - Dashboard available at http://localhost:9000
[LOW] 2024-01-15T10:30:01.000Z - Running health check...
[LOW] 2024-01-15T10:30:02.500Z - All services healthy
[CRITICAL] 2024-01-15T10:35:01.000Z - ComfyUI connection failed: ECONNREFUSED
[HIGH] 2024-01-15T10:35:01.100Z - 1 services have issues
```

## Next Steps

This basic monitor can be extended with:
- WebSocket connection monitoring
- API response validation
- Email/webhook alerts
- Historical data storage
- Performance metrics
- Custom health checks

The system runs completely independently and won't interfere with your main application.
