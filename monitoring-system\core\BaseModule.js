/**
 * Base Module Class - All monitoring modules extend this
 * Provides common functionality and standardized interface
 */

class BaseModule {
  constructor(name, eventBus, config = {}) {
    this.name = name;
    this.eventBus = eventBus;
    this.config = { enabled: true, ...config };
    this.status = 'stopped';
    this.listeners = [];
    this.intervals = [];
    this.timeouts = [];
    this.startTime = null;
    this.lastActivity = null;
  }

  /**
   * Start the module
   */
  async start() {
    if (this.status === 'running') {
      throw new Error(`Module ${this.name} is already running`);
    }

    if (!this.config.enabled) {
      this.log('Module is disabled, skipping start');
      return;
    }

    this.status = 'starting';
    this.startTime = Date.now();
    
    try {
      await this.onStart();
      this.status = 'running';
      this.log('Module started successfully');
      this.emit('module:started', { module: this.name });
    } catch (error) {
      this.status = 'error';
      this.log(`Failed to start: ${error.message}`, 'error');
      this.emit('module:error', { module: this.name, error: error.message });
      throw error;
    }
  }

  /**
   * Stop the module
   */
  async stop() {
    if (this.status === 'stopped') {
      return;
    }

    this.status = 'stopping';
    
    try {
      // Clear all intervals and timeouts
      this.intervals.forEach(id => clearInterval(id));
      this.timeouts.forEach(id => clearTimeout(id));
      this.intervals = [];
      this.timeouts = [];

      // Remove all event listeners
      this.listeners.forEach(({ event, listenerId }) => {
        this.eventBus.off(event, listenerId);
      });
      this.listeners = [];

      await this.onStop();
      this.status = 'stopped';
      this.log('Module stopped successfully');
      this.emit('module:stopped', { module: this.name });
    } catch (error) {
      this.status = 'error';
      this.log(`Failed to stop: ${error.message}`, 'error');
      this.emit('module:error', { module: this.name, error: error.message });
      throw error;
    }
  }

  /**
   * Override in child classes
   */
  async onStart() {
    // Override in child classes
  }

  /**
   * Override in child classes
   */
  async onStop() {
    // Override in child classes
  }

  /**
   * Emit an event through the event bus
   */
  emit(event, data = {}) {
    this.lastActivity = Date.now();
    return this.eventBus.emit(event, { ...data, source: this.name });
  }

  /**
   * Listen to events (automatically tracked for cleanup)
   */
  listen(event, callback, options = {}) {
    const listenerId = this.eventBus.on(event, callback, options);
    this.listeners.push({ event, listenerId });
    return listenerId;
  }

  /**
   * Set interval (automatically tracked for cleanup)
   */
  setInterval(callback, interval) {
    const id = setInterval(callback, interval);
    this.intervals.push(id);
    return id;
  }

  /**
   * Set timeout (automatically tracked for cleanup)
   */
  setTimeout(callback, timeout) {
    const id = setTimeout(callback, timeout);
    this.timeouts.push(id);
    return id;
  }

  /**
   * Log with module context
   */
  log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    const logData = {
      module: this.name,
      message,
      level,
      timestamp
    };

    console.log(`[${this.name}] ${timestamp} - ${message}`);
    this.emit('module:log', logData);
  }

  /**
   * Get module status
   */
  getStatus() {
    return {
      name: this.name,
      status: this.status,
      enabled: this.config.enabled,
      startTime: this.startTime,
      lastActivity: this.lastActivity,
      uptime: this.startTime ? Date.now() - this.startTime : 0,
      listeners: this.listeners.length,
      intervals: this.intervals.length,
      timeouts: this.timeouts.length
    };
  }

  /**
   * Update module configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.emit('module:config-updated', { module: this.name, config: this.config });
  }
}

module.exports = BaseModule;
