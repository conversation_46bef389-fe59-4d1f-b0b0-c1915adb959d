/**
 * Core Event Bus - Foundation for modular monitoring system
 * All modules communicate through this event system
 */

class EventBus {
  constructor() {
    this.listeners = new Map();
    this.history = [];
    this.maxHistory = 1000;
  }

  /**
   * Subscribe to events
   * @param {string} event - Event name
   * @param {function} callback - Callback function
   * @param {object} options - Options (once, priority)
   */
  on(event, callback, options = {}) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }

    const listener = {
      callback,
      once: options.once || false,
      priority: options.priority || 0,
      id: Math.random().toString(36).substr(2, 9)
    };

    this.listeners.get(event).push(listener);
    
    // Sort by priority (higher priority first)
    this.listeners.get(event).sort((a, b) => b.priority - a.priority);

    return listener.id;
  }

  /**
   * Unsubscribe from events
   */
  off(event, listenerId) {
    if (!this.listeners.has(event)) return;

    const listeners = this.listeners.get(event);
    const index = listeners.findIndex(l => l.id === listenerId);
    
    if (index !== -1) {
      listeners.splice(index, 1);
    }
  }

  /**
   * Emit an event
   */
  emit(event, data = {}) {
    const eventData = {
      event,
      data,
      timestamp: Date.now(),
      id: Math.random().toString(36).substr(2, 9)
    };

    // Add to history
    this.history.unshift(eventData);
    if (this.history.length > this.maxHistory) {
      this.history = this.history.slice(0, this.maxHistory);
    }

    // Call listeners
    if (this.listeners.has(event)) {
      const listeners = [...this.listeners.get(event)]; // Copy to avoid modification during iteration
      
      for (const listener of listeners) {
        try {
          listener.callback(eventData.data, eventData);
          
          // Remove if it's a one-time listener
          if (listener.once) {
            this.off(event, listener.id);
          }
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      }
    }

    return eventData.id;
  }

  /**
   * Get event history
   */
  getHistory(event = null, limit = 100) {
    let history = this.history;
    
    if (event) {
      history = history.filter(h => h.event === event);
    }
    
    return history.slice(0, limit);
  }

  /**
   * Clear event history
   */
  clearHistory() {
    this.history = [];
  }

  /**
   * Get all registered events
   */
  getEvents() {
    return Array.from(this.listeners.keys());
  }

  /**
   * Get listener count for an event
   */
  getListenerCount(event) {
    return this.listeners.has(event) ? this.listeners.get(event).length : 0;
  }
}

module.exports = EventBus;
