/**
 * Module Manager - Orchestrates all monitoring modules
 * Handles loading, starting, stopping, and communication between modules
 */

const EventBus = require('./EventBus');

class ModuleManager {
  constructor() {
    this.eventBus = new EventBus();
    this.modules = new Map();
    this.startTime = null;
    this.setupCoreEventHandlers();
  }

  setupCoreEventHandlers() {
    // Log all module events
    this.eventBus.on('module:log', (data) => {
      // Could forward to external logging system
    });

    // Handle module errors
    this.eventBus.on('module:error', (data) => {
      console.error(`Module ${data.module} error: ${data.error}`);
    });

    // Track module lifecycle
    this.eventBus.on('module:started', (data) => {
      console.log(`✅ Module ${data.module} started`);
    });

    this.eventBus.on('module:stopped', (data) => {
      console.log(`⏹️  Module ${data.module} stopped`);
    });
  }

  /**
   * Register a module
   */
  registerModule(name, moduleInstance) {
    if (this.modules.has(name)) {
      throw new Error(`Module ${name} is already registered`);
    }

    this.modules.set(name, moduleInstance);
    console.log(`📦 Registered module: ${name}`);
    
    this.eventBus.emit('manager:module-registered', {
      module: name,
      status: moduleInstance.getStatus()
    });

    return this;
  }

  /**
   * Start a specific module
   */
  async startModule(name) {
    const module = this.modules.get(name);
    if (!module) {
      throw new Error(`Module ${name} not found`);
    }

    await module.start();
    return this;
  }

  /**
   * Stop a specific module
   */
  async stopModule(name) {
    const module = this.modules.get(name);
    if (!module) {
      throw new Error(`Module ${name} not found`);
    }

    await module.stop();
    return this;
  }

  /**
   * Start all modules
   */
  async startAll() {
    this.startTime = Date.now();
    console.log('🚀 Starting all monitoring modules...');

    const startPromises = Array.from(this.modules.entries()).map(async ([name, module]) => {
      try {
        await module.start();
        return { name, success: true };
      } catch (error) {
        console.error(`Failed to start module ${name}:`, error.message);
        return { name, success: false, error: error.message };
      }
    });

    const results = await Promise.all(startPromises);
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success);

    console.log(`✅ Started ${successful}/${results.length} modules`);
    
    if (failed.length > 0) {
      console.log('❌ Failed modules:', failed.map(f => f.name).join(', '));
    }

    this.eventBus.emit('manager:startup-completed', {
      total: results.length,
      successful,
      failed: failed.length,
      results
    });

    return this;
  }

  /**
   * Stop all modules
   */
  async stopAll() {
    console.log('⏹️  Stopping all monitoring modules...');

    const stopPromises = Array.from(this.modules.entries()).map(async ([name, module]) => {
      try {
        await module.stop();
        return { name, success: true };
      } catch (error) {
        console.error(`Failed to stop module ${name}:`, error.message);
        return { name, success: false, error: error.message };
      }
    });

    const results = await Promise.all(stopPromises);
    const successful = results.filter(r => r.success).length;

    console.log(`⏹️  Stopped ${successful}/${results.length} modules`);

    this.eventBus.emit('manager:shutdown-completed', {
      total: results.length,
      successful,
      results
    });

    return this;
  }

  /**
   * Get status of all modules
   */
  getStatus() {
    const moduleStatuses = {};
    
    for (const [name, module] of this.modules.entries()) {
      moduleStatuses[name] = module.getStatus();
    }

    return {
      manager: {
        startTime: this.startTime,
        uptime: this.startTime ? Date.now() - this.startTime : 0,
        moduleCount: this.modules.size,
        runningModules: Object.values(moduleStatuses).filter(s => s.status === 'running').length
      },
      modules: moduleStatuses,
      eventBus: {
        totalEvents: this.eventBus.getEvents().length,
        historySize: this.eventBus.getHistory().length
      }
    };
  }

  /**
   * Get a specific module
   */
  getModule(name) {
    return this.modules.get(name);
  }

  /**
   * Get all module names
   */
  getModuleNames() {
    return Array.from(this.modules.keys());
  }

  /**
   * Access to event bus for external listeners
   */
  getEventBus() {
    return this.eventBus;
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    console.log('🔄 Initiating graceful shutdown...');
    await this.stopAll();
    console.log('✅ Shutdown completed');
  }
}

module.exports = ModuleManager;
