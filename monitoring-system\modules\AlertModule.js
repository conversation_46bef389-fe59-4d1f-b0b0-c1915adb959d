/**
 * Alert & Logging Module
 * Handles alert management, file logging, and notification routing
 */

const BaseModule = require('../core/BaseModule');
const fs = require('fs');
const path = require('path');

class AlertModule extends BaseModule {
  constructor(eventBus, config = {}) {
    const defaultConfig = {
      logFile: './monitoring-alerts.log',
      maxLogSize: 10 * 1024 * 1024, // 10MB
      rotateLogFiles: true,
      maxLogFiles: 5,
      alertLevels: {
        CRITICAL: { priority: 1, color: '\x1b[41m', retention: 7 * 24 * 60 * 60 * 1000 }, // 7 days
        HIGH: { priority: 2, color: '\x1b[31m', retention: 3 * 24 * 60 * 60 * 1000 },     // 3 days
        MEDIUM: { priority: 3, color: '\x1b[33m', retention: 1 * 24 * 60 * 60 * 1000 },   // 1 day
        LOW: { priority: 4, color: '\x1b[36m', retention: 12 * 60 * 60 * 1000 }           // 12 hours
      },
      notifications: {
        console: true,
        file: true,
        webhook: false,
        email: false
      }
    };

    super('Alert', eventBus, { ...defaultConfig, ...config });
    this.alerts = [];
    this.alertCounts = { CRITICAL: 0, HIGH: 0, MEDIUM: 0, LOW: 0 };
    this.setupEventListeners();
  }

  async onStart() {
    this.log('Starting alert management...');
    
    // Ensure log directory exists
    const logDir = path.dirname(this.config.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // Start periodic cleanup
    this.setInterval(() => {
      this.cleanupOldAlerts();
      this.rotateLogIfNeeded();
    }, 60000); // Every minute

    this.writeToLog('Alert system started', 'LOW');
  }

  async onStop() {
    this.log('Stopping alert management...');
    this.writeToLog('Alert system stopped', 'LOW');
  }

  setupEventListeners() {
    // Listen to all health-related events
    this.listen('health:service-down', (data) => {
      this.createAlert('CRITICAL', `Service ${data.service} is DOWN`, data);
    });

    this.listen('health:service-unhealthy', (data) => {
      this.createAlert('HIGH', `Service ${data.service} is unhealthy (HTTP ${data.statusCode})`, data);
    });

    this.listen('health:service-recovered', (data) => {
      this.createAlert('LOW', `Service ${data.service} has recovered`, data);
    });

    this.listen('health:slow-response', (data) => {
      this.createAlert('MEDIUM', `Service ${data.service} slow response: ${data.responseTime}ms`, data);
    });

    // Listen to WebSocket events
    this.listen('websocket:disconnected', (data) => {
      this.createAlert('HIGH', `WebSocket ${data.name} disconnected (${data.code})`, data);
    });

    this.listen('websocket:error', (data) => {
      this.createAlert('CRITICAL', `WebSocket ${data.name} error: ${data.error}`, data);
    });

    this.listen('websocket:connected', (data) => {
      this.createAlert('LOW', `WebSocket ${data.name} connected`, data);
    });

    this.listen('websocket:max-reconnects-reached', (data) => {
      this.createAlert('CRITICAL', `WebSocket ${data.name} failed to reconnect after ${data.attempts} attempts`, data);
    });

    // Listen to module events
    this.listen('module:error', (data) => {
      this.createAlert('HIGH', `Module ${data.module} error: ${data.error}`, data);
    });
  }

  createAlert(level, message, data = {}) {
    const alert = {
      id: Math.random().toString(36).substr(2, 9),
      level,
      message,
      data,
      timestamp: Date.now(),
      acknowledged: false,
      source: data.source || 'system'
    };

    // Add to alerts array
    this.alerts.unshift(alert);
    this.alertCounts[level]++;

    // Process notifications
    this.processNotifications(alert);

    // Emit alert event
    this.emit('alert:created', alert);

    return alert.id;
  }

  processNotifications(alert) {
    const levelConfig = this.config.alertLevels[alert.level];
    
    // Console notification
    if (this.config.notifications.console) {
      const colorCode = levelConfig.color;
      const timestamp = new Date(alert.timestamp).toISOString();
      console.log(`${colorCode}[${alert.level}]\x1b[0m ${timestamp} - ${alert.message}`);
    }

    // File notification
    if (this.config.notifications.file) {
      this.writeToLog(alert.message, alert.level, alert.data);
    }

    // Webhook notification (placeholder)
    if (this.config.notifications.webhook && (alert.level === 'CRITICAL' || alert.level === 'HIGH')) {
      this.sendWebhookNotification(alert);
    }

    // Email notification (placeholder)
    if (this.config.notifications.email && alert.level === 'CRITICAL') {
      this.sendEmailNotification(alert);
    }
  }

  writeToLog(message, level, data = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      data: Object.keys(data).length > 0 ? data : undefined
    };

    const logLine = JSON.stringify(logEntry) + '\n';
    
    try {
      fs.appendFileSync(this.config.logFile, logLine);
    } catch (error) {
      console.error('Failed to write to log file:', error.message);
    }
  }

  rotateLogIfNeeded() {
    try {
      const stats = fs.statSync(this.config.logFile);
      
      if (stats.size > this.config.maxLogSize && this.config.rotateLogFiles) {
        this.rotateLogFile();
      }
    } catch (error) {
      // File doesn't exist yet, ignore
    }
  }

  rotateLogFile() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const rotatedFile = this.config.logFile.replace('.log', `-${timestamp}.log`);
    
    try {
      fs.renameSync(this.config.logFile, rotatedFile);
      this.log(`Log file rotated to ${rotatedFile}`);
      
      // Clean up old log files
      this.cleanupOldLogFiles();
    } catch (error) {
      console.error('Failed to rotate log file:', error.message);
    }
  }

  cleanupOldLogFiles() {
    const logDir = path.dirname(this.config.logFile);
    const logBaseName = path.basename(this.config.logFile, '.log');
    
    try {
      const files = fs.readdirSync(logDir)
        .filter(file => file.startsWith(logBaseName) && file.endsWith('.log') && file !== path.basename(this.config.logFile))
        .map(file => ({
          name: file,
          path: path.join(logDir, file),
          stats: fs.statSync(path.join(logDir, file))
        }))
        .sort((a, b) => b.stats.mtime - a.stats.mtime);

      // Keep only the most recent log files
      if (files.length > this.config.maxLogFiles) {
        const filesToDelete = files.slice(this.config.maxLogFiles);
        filesToDelete.forEach(file => {
          fs.unlinkSync(file.path);
          this.log(`Deleted old log file: ${file.name}`);
        });
      }
    } catch (error) {
      console.error('Failed to cleanup old log files:', error.message);
    }
  }

  cleanupOldAlerts() {
    const now = Date.now();
    let cleanedCount = 0;

    this.alerts = this.alerts.filter(alert => {
      const levelConfig = this.config.alertLevels[alert.level];
      const age = now - alert.timestamp;
      
      if (age > levelConfig.retention) {
        this.alertCounts[alert.level]--;
        cleanedCount++;
        return false;
      }
      return true;
    });

    if (cleanedCount > 0) {
      this.log(`Cleaned up ${cleanedCount} old alerts`);
    }
  }

  sendWebhookNotification(alert) {
    // Placeholder for webhook implementation
    this.log(`Webhook notification would be sent for: ${alert.message}`);
  }

  sendEmailNotification(alert) {
    // Placeholder for email implementation
    this.log(`Email notification would be sent for: ${alert.message}`);
  }

  // Public API methods
  getAlerts(level = null, limit = 100) {
    let alerts = this.alerts;
    
    if (level) {
      alerts = alerts.filter(a => a.level === level);
    }
    
    return alerts.slice(0, limit);
  }

  getAlertCounts() {
    return { ...this.alertCounts };
  }

  acknowledgeAlert(alertId) {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      this.emit('alert:acknowledged', { alertId, timestamp: Date.now() });
      return true;
    }
    return false;
  }

  clearAlerts(level = null) {
    if (level) {
      const beforeCount = this.alerts.length;
      this.alerts = this.alerts.filter(a => a.level !== level);
      const cleared = beforeCount - this.alerts.length;
      this.alertCounts[level] = 0;
      this.log(`Cleared ${cleared} ${level} alerts`);
    } else {
      const cleared = this.alerts.length;
      this.alerts = [];
      this.alertCounts = { CRITICAL: 0, HIGH: 0, MEDIUM: 0, LOW: 0 };
      this.log(`Cleared all ${cleared} alerts`);
    }
  }
}

module.exports = AlertModule;
