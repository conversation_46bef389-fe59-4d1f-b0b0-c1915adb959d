/**
 * HTTP Health Monitoring Module
 * Small, focused module that checks HTTP service health
 */

const BaseModule = require('../core/BaseModule');
const http = require('http');
const https = require('https');

class HttpHealthModule extends BaseModule {
  constructor(eventBus, config = {}) {
    const defaultConfig = {
      services: {
        comfyui: { url: 'http://127.0.0.1:8188', timeout: 5000 },
        fastapi: { url: 'http://127.0.0.1:8000/health', timeout: 5000 },
        nextjs: { url: 'http://127.0.0.1:3003/api/v1/health', timeout: 5000 },
        ollama: { url: 'http://127.0.0.1:11434/api/tags', timeout: 5000 }
      },
      checkInterval: 30000, // 30 seconds
      retries: 3,
      slowResponseThreshold: 3000 // 3 seconds
    };

    super('HttpHealth', eventBus, { ...defaultConfig, ...config });
    this.serviceStates = new Map();
  }

  async onStart() {
    this.log('Starting HTTP health monitoring...');
    
    // Start periodic health checks
    this.setInterval(() => {
      this.checkAllServices();
    }, this.config.checkInterval);

    // Run initial check
    await this.checkAllServices();
  }

  async onStop() {
    this.log('Stopping HTTP health monitoring...');
  }

  async checkAllServices() {
    this.log('Running health check cycle...');
    
    const promises = Object.entries(this.config.services).map(([name, config]) =>
      this.checkService(name, config)
    );

    const results = await Promise.all(promises);
    
    this.emit('health:check-completed', {
      results,
      timestamp: Date.now(),
      totalServices: results.length,
      healthyServices: results.filter(r => r.status === 'healthy').length
    });
  }

  async checkService(serviceName, serviceConfig) {
    const startTime = Date.now();
    const previousState = this.serviceStates.get(serviceName);
    
    let result = {
      service: serviceName,
      url: serviceConfig.url,
      status: 'unknown',
      responseTime: 0,
      timestamp: Date.now(),
      error: null,
      statusCode: null
    };

    try {
      const response = await this.makeHttpRequest(serviceConfig.url, serviceConfig.timeout);
      result.responseTime = Date.now() - startTime;
      result.statusCode = response.statusCode;

      if (response.statusCode >= 200 && response.statusCode < 300) {
        result.status = 'healthy';
        
        // Check for slow response
        if (result.responseTime > this.config.slowResponseThreshold) {
          this.emit('health:slow-response', {
            service: serviceName,
            responseTime: result.responseTime,
            threshold: this.config.slowResponseThreshold
          });
        }
      } else {
        result.status = 'unhealthy';
        result.error = `HTTP ${response.statusCode}`;
      }

    } catch (error) {
      result.responseTime = Date.now() - startTime;
      result.status = 'down';
      result.error = error.message;
    }

    // Detect state changes
    if (previousState && previousState.status !== result.status) {
      this.emit('health:state-changed', {
        service: serviceName,
        previousStatus: previousState.status,
        newStatus: result.status,
        previousCheck: previousState.timestamp,
        currentCheck: result.timestamp
      });
    }

    // Store current state
    this.serviceStates.set(serviceName, result);

    // Emit specific events based on status
    if (result.status === 'down') {
      this.emit('health:service-down', result);
    } else if (result.status === 'unhealthy') {
      this.emit('health:service-unhealthy', result);
    } else if (result.status === 'healthy' && previousState?.status !== 'healthy') {
      this.emit('health:service-recovered', result);
    }

    return result;
  }

  makeHttpRequest(url, timeout) {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const client = urlObj.protocol === 'https:' ? https : http;
      
      const req = client.get(url, { timeout }, (res) => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers
        });
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      req.on('error', reject);
    });
  }

  // Public API methods
  getServiceStates() {
    return Object.fromEntries(this.serviceStates);
  }

  getServiceState(serviceName) {
    return this.serviceStates.get(serviceName) || null;
  }

  addService(name, config) {
    this.config.services[name] = config;
    this.log(`Added service: ${name}`);
    this.emit('health:service-added', { service: name, config });
  }

  removeService(name) {
    delete this.config.services[name];
    this.serviceStates.delete(name);
    this.log(`Removed service: ${name}`);
    this.emit('health:service-removed', { service: name });
  }
}

module.exports = HttpHealthModule;
