/**
 * WebSocket Monitoring Module
 * Monitors WebSocket connections for stability and message flow
 */

const BaseModule = require('../core/BaseModule');
const WebSocket = require('ws');

class WebSocketModule extends BaseModule {
  constructor(eventBus, config = {}) {
    const defaultConfig = {
      connections: {
        backend: { 
          url: 'ws://127.0.0.1:8000/ws', 
          name: 'FastAPI Backend',
          reconnectInterval: 5000,
          pingInterval: 30000
        },
        comfyui: { 
          url: 'ws://127.0.0.1:8188/ws', 
          name: 'ComfyUI',
          reconnectInterval: 5000,
          pingInterval: 30000
        }
      },
      messageTimeout: 10000, // 10 seconds
      maxReconnectAttempts: 5
    };

    super('WebSocket', eventBus, { ...defaultConfig, ...config });
    this.connections = new Map();
    this.connectionStates = new Map();
    this.messageCounters = new Map();
  }

  async onStart() {
    this.log('Starting WebSocket monitoring...');
    
    // Initialize all configured connections
    for (const [key, config] of Object.entries(this.config.connections)) {
      await this.initializeConnection(key, config);
    }
  }

  async onStop() {
    this.log('Stopping WebSocket monitoring...');
    
    // Close all connections
    for (const [key, connection] of this.connections.entries()) {
      if (connection.ws && connection.ws.readyState === WebSocket.OPEN) {
        connection.ws.close();
      }
    }
    
    this.connections.clear();
    this.connectionStates.clear();
  }

  async initializeConnection(key, config) {
    this.log(`Initializing WebSocket connection: ${key}`);
    
    const connectionData = {
      key,
      config,
      ws: null,
      reconnectAttempts: 0,
      lastConnected: null,
      lastDisconnected: null,
      lastPing: null,
      lastPong: null
    };

    this.connections.set(key, connectionData);
    this.connectionStates.set(key, {
      status: 'disconnected',
      lastStatusChange: Date.now(),
      totalConnections: 0,
      totalDisconnections: 0,
      totalReconnects: 0,
      averageLatency: 0,
      lastError: null
    });

    this.messageCounters.set(key, {
      sent: 0,
      received: 0,
      errors: 0,
      lastActivity: null
    });

    await this.connectWebSocket(key);
  }

  async connectWebSocket(key) {
    const connection = this.connections.get(key);
    const state = this.connectionStates.get(key);
    
    if (!connection) return;

    try {
      this.log(`Connecting to ${connection.config.name}...`);
      
      const ws = new WebSocket(connection.config.url);
      connection.ws = ws;

      ws.on('open', () => {
        this.handleConnectionOpen(key);
      });

      ws.on('close', (code, reason) => {
        this.handleConnectionClose(key, code, reason);
      });

      ws.on('error', (error) => {
        this.handleConnectionError(key, error);
      });

      ws.on('message', (data) => {
        this.handleMessage(key, data);
      });

      ws.on('pong', () => {
        this.handlePong(key);
      });

      // Set up ping interval
      if (connection.config.pingInterval) {
        const pingInterval = setInterval(() => {
          if (ws.readyState === WebSocket.OPEN) {
            connection.lastPing = Date.now();
            ws.ping();
          } else {
            clearInterval(pingInterval);
          }
        }, connection.config.pingInterval);
      }

    } catch (error) {
      this.handleConnectionError(key, error);
    }
  }

  handleConnectionOpen(key) {
    const connection = this.connections.get(key);
    const state = this.connectionStates.get(key);
    
    connection.lastConnected = Date.now();
    connection.reconnectAttempts = 0;
    
    state.status = 'connected';
    state.lastStatusChange = Date.now();
    state.totalConnections++;

    this.log(`✅ ${connection.config.name} connected`);
    
    this.emit('websocket:connected', {
      connection: key,
      name: connection.config.name,
      url: connection.config.url,
      timestamp: Date.now()
    });
  }

  handleConnectionClose(key, code, reason) {
    const connection = this.connections.get(key);
    const state = this.connectionStates.get(key);
    
    connection.lastDisconnected = Date.now();
    state.status = 'disconnected';
    state.lastStatusChange = Date.now();
    state.totalDisconnections++;

    this.log(`❌ ${connection.config.name} disconnected (${code}: ${reason})`);
    
    this.emit('websocket:disconnected', {
      connection: key,
      name: connection.config.name,
      code,
      reason: reason.toString(),
      timestamp: Date.now()
    });

    // Schedule reconnection
    this.scheduleReconnection(key);
  }

  handleConnectionError(key, error) {
    const connection = this.connections.get(key);
    const state = this.connectionStates.get(key);
    const counters = this.messageCounters.get(key);
    
    state.lastError = error.message;
    counters.errors++;

    this.log(`🔴 ${connection.config.name} error: ${error.message}`);
    
    this.emit('websocket:error', {
      connection: key,
      name: connection.config.name,
      error: error.message,
      timestamp: Date.now()
    });
  }

  handleMessage(key, data) {
    const counters = this.messageCounters.get(key);
    counters.received++;
    counters.lastActivity = Date.now();

    // Try to parse message for additional validation
    try {
      const message = JSON.parse(data.toString());
      
      this.emit('websocket:message-received', {
        connection: key,
        messageType: message.type || 'unknown',
        size: data.length,
        timestamp: Date.now()
      });
      
    } catch (parseError) {
      this.emit('websocket:invalid-message', {
        connection: key,
        error: 'Invalid JSON',
        size: data.length,
        timestamp: Date.now()
      });
    }
  }

  handlePong(key) {
    const connection = this.connections.get(key);
    const state = this.connectionStates.get(key);
    
    if (connection.lastPing) {
      const latency = Date.now() - connection.lastPing;
      
      // Update average latency
      if (state.averageLatency === 0) {
        state.averageLatency = latency;
      } else {
        state.averageLatency = (state.averageLatency + latency) / 2;
      }
      
      connection.lastPong = Date.now();
      
      this.emit('websocket:pong', {
        connection: key,
        latency,
        averageLatency: state.averageLatency,
        timestamp: Date.now()
      });
    }
  }

  scheduleReconnection(key) {
    const connection = this.connections.get(key);
    
    if (connection.reconnectAttempts >= this.config.maxReconnectAttempts) {
      this.log(`❌ Max reconnection attempts reached for ${connection.config.name}`);
      
      this.emit('websocket:max-reconnects-reached', {
        connection: key,
        name: connection.config.name,
        attempts: connection.reconnectAttempts,
        timestamp: Date.now()
      });
      return;
    }

    connection.reconnectAttempts++;
    
    this.log(`🔄 Scheduling reconnection for ${connection.config.name} (attempt ${connection.reconnectAttempts})`);
    
    this.setTimeout(() => {
      this.connectWebSocket(key);
    }, connection.config.reconnectInterval);
  }

  // Public API methods
  getConnectionStates() {
    const states = {};
    for (const [key, state] of this.connectionStates.entries()) {
      const connection = this.connections.get(key);
      const counters = this.messageCounters.get(key);
      
      states[key] = {
        ...state,
        name: connection.config.name,
        url: connection.config.url,
        reconnectAttempts: connection.reconnectAttempts,
        lastConnected: connection.lastConnected,
        lastDisconnected: connection.lastDisconnected,
        messageCounters: { ...counters }
      };
    }
    return states;
  }

  sendTestMessage(key, message) {
    const connection = this.connections.get(key);
    if (connection && connection.ws && connection.ws.readyState === WebSocket.OPEN) {
      const counters = this.messageCounters.get(key);
      counters.sent++;
      
      connection.ws.send(JSON.stringify(message));
      
      this.emit('websocket:message-sent', {
        connection: key,
        message,
        timestamp: Date.now()
      });
      
      return true;
    }
    return false;
  }
}

module.exports = WebSocketModule;
