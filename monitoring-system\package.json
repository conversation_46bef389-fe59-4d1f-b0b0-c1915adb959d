{"name": "comfyui-modular-monitoring", "version": "1.0.0", "description": "Modular monitoring system for ComfyUI application stack", "main": "start.js", "scripts": {"start": "node start.js", "dev": "nodemon start.js", "test": "echo \"No tests yet\" && exit 0"}, "keywords": ["monitoring", "comfyui", "modular", "health-check"], "author": "ComfyUI Monitoring", "license": "MIT", "dependencies": {"ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}