#!/usr/bin/env node

/**
 * Simple ComfyUI Monitoring System
 * Independent monitoring for your ComfyUI application stack
 * Usage: node simple-monitor.js
 */

const http = require('http');
const fs = require('fs');

// Configuration
const SERVICES = {
  'ComfyUI': 'http://127.0.0.1:8188',
  'FastAPI': 'http://127.0.0.1:8000/health',
  'Next.js': 'http://127.0.0.1:3003/api/v1/health',
  'Ollama': 'http://127.0.0.1:11434/api/tags'
};

const SEVERITY = {
  CRITICAL: '\x1b[41m[CRITICAL]\x1b[0m',
  HIGH: '\x1b[31m[HIGH]\x1b[0m',
  MEDIUM: '\x1b[33m[MEDIUM]\x1b[0m',
  LOW: '\x1b[36m[LOW]\x1b[0m'
};

let alerts = [];

function log(message, severity = SEVERITY.LOW) {
  const timestamp = new Date().toISOString();
  const logEntry = `${severity} ${timestamp} - ${message}`;
  console.log(logEntry);
  
  // Save to file
  fs.appendFileSync('monitoring.log', logEntry + '\n');
  
  // Keep in memory for dashboard
  alerts.unshift({ timestamp, message, severity });
  if (alerts.length > 50) alerts = alerts.slice(0, 50);
}

function checkService(name, url) {
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    const req = http.get(url, { timeout: 5000 }, (res) => {
      const responseTime = Date.now() - startTime;
      
      if (res.statusCode >= 200 && res.statusCode < 300) {
        if (responseTime > 3000) {
          log(`${name} slow response: ${responseTime}ms`, SEVERITY.MEDIUM);
        }
        resolve({ name, status: 'OK', responseTime });
      } else {
        log(`${name} returned HTTP ${res.statusCode}`, SEVERITY.HIGH);
        resolve({ name, status: 'ERROR', responseTime });
      }
    });
    
    req.on('timeout', () => {
      req.destroy();
      log(`${name} timeout`, SEVERITY.CRITICAL);
      resolve({ name, status: 'TIMEOUT', responseTime: Date.now() - startTime });
    });
    
    req.on('error', (err) => {
      log(`${name} connection failed: ${err.message}`, SEVERITY.CRITICAL);
      resolve({ name, status: 'FAILED', error: err.message });
    });
  });
}

async function runCheck() {
  log('Running health check...', SEVERITY.LOW);
  
  const results = await Promise.all(
    Object.entries(SERVICES).map(([name, url]) => checkService(name, url))
  );
  
  const failed = results.filter(r => r.status !== 'OK');
  if (failed.length === 0) {
    log('All services healthy', SEVERITY.LOW);
  } else {
    log(`${failed.length} services have issues`, SEVERITY.HIGH);
  }
  
  return results;
}

// Simple web dashboard
function createDashboard() {
  const server = http.createServer((req, res) => {
    if (req.url === '/') {
      res.writeHead(200, { 'Content-Type': 'text/html' });
      res.end(`
        <html>
          <head><title>ComfyUI Monitor</title></head>
          <body>
            <h1>ComfyUI Monitoring Dashboard</h1>
            <div id="status">Loading...</div>
            <h2>Recent Alerts</h2>
            <div id="alerts">${alerts.map(a => `<p>${a.timestamp} - ${a.message}</p>`).join('')}</div>
            <script>
              setInterval(() => {
                fetch('/api/status').then(r => r.json()).then(data => {
                  document.getElementById('status').innerHTML = 
                    '<h2>Service Status</h2>' + 
                    data.map(s => '<p>' + s.name + ': ' + s.status + '</p>').join('');
                });
              }, 5000);
            </script>
          </body>
        </html>
      `);
    } else if (req.url === '/api/status') {
      runCheck().then(results => {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(results));
      });
    } else {
      res.writeHead(404);
      res.end('Not found');
    }
  });
  
  server.listen(9000, () => {
    log('Dashboard available at http://localhost:9000', SEVERITY.LOW);
  });
}

// Main execution
log('Starting ComfyUI Monitoring System...', SEVERITY.LOW);
createDashboard();

// Run initial check
runCheck();

// Schedule regular checks every 30 seconds
setInterval(runCheck, 30000);

log('Monitoring system started. Press Ctrl+C to stop.', SEVERITY.LOW);
