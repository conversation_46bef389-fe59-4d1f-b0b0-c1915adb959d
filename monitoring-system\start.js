#!/usr/bin/env node

/**
 * Modular ComfyUI Monitoring System Starter
 * Start with basic HTTP health monitoring, easily add more modules
 */

const ModuleManager = require('./core/ModuleManager');
const HttpHealthModule = require('./modules/HttpHealthModule');
const WebSocketModule = require('./modules/WebSocketModule');
const AlertModule = require('./modules/AlertModule');

async function main() {
  console.log('🔍 ComfyUI Modular Monitoring System');
  console.log('=====================================');

  // Create module manager
  const manager = new ModuleManager();

  // Get event bus for listening to events
  const eventBus = manager.getEventBus();

  // Set up event listeners for demonstration
  eventBus.on('health:service-down', (data) => {
    console.log(`🔴 CRITICAL: ${data.service} is DOWN - ${data.error}`);
  });

  eventBus.on('health:service-unhealthy', (data) => {
    console.log(`🟠 HIGH: ${data.service} is unhealthy - HTTP ${data.statusCode}`);
  });

  eventBus.on('health:service-recovered', (data) => {
    console.log(`🟢 LOW: ${data.service} has recovered`);
  });

  eventBus.on('health:slow-response', (data) => {
    console.log(`🟡 MEDIUM: ${data.service} slow response - ${data.responseTime}ms`);
  });

  eventBus.on('health:check-completed', (data) => {
    console.log(`📊 Health check: ${data.healthyServices}/${data.totalServices} services healthy`);
  });

  // WebSocket event listeners
  eventBus.on('websocket:connected', (data) => {
    console.log(`🟢 LOW: WebSocket ${data.name} connected`);
  });

  eventBus.on('websocket:disconnected', (data) => {
    console.log(`🔴 HIGH: WebSocket ${data.name} disconnected (${data.code})`);
  });

  eventBus.on('websocket:error', (data) => {
    console.log(`🔴 CRITICAL: WebSocket ${data.name} error - ${data.error}`);
  });

  eventBus.on('websocket:max-reconnects-reached', (data) => {
    console.log(`🔴 CRITICAL: WebSocket ${data.name} failed to reconnect after ${data.attempts} attempts`);
  });

  // Register modules
  console.log('\n📦 Registering modules...');
  
  // Start with just HTTP health monitoring
  const httpHealth = new HttpHealthModule(eventBus, {
    checkInterval: 15000, // Check every 15 seconds for demo
    services: {
      comfyui: { url: 'http://127.0.0.1:8188', timeout: 5000 },
      fastapi: { url: 'http://127.0.0.1:8000/health', timeout: 5000 },
      nextjs: { url: 'http://127.0.0.1:3003/api/v1/health', timeout: 5000 },
      ollama: { url: 'http://127.0.0.1:11434/api/tags', timeout: 5000 }
    }
  });

  manager.registerModule('httpHealth', httpHealth);

  // Add WebSocket monitoring
  const webSocketMonitor = new WebSocketModule(eventBus, {
    connections: {
      backend: {
        url: 'ws://127.0.0.1:8000/ws',
        name: 'FastAPI Backend',
        reconnectInterval: 3000,
        pingInterval: 20000
      },
      comfyui: {
        url: 'ws://127.0.0.1:8188/ws',
        name: 'ComfyUI',
        reconnectInterval: 3000,
        pingInterval: 20000
      }
    }
  });

  manager.registerModule('webSocket', webSocketMonitor);

  // Add Alert & Logging system
  const alertSystem = new AlertModule(eventBus, {
    logFile: './logs/monitoring-alerts.log',
    notifications: {
      console: false, // We'll handle console output in main script
      file: true,
      webhook: false,
      email: false
    }
  });

  manager.registerModule('alerts', alertSystem);

  // Start all modules
  console.log('\n🚀 Starting modules...');
  await manager.startAll();

  console.log('\n✅ Monitoring system is running!');
  console.log('Press Ctrl+C to stop\n');

  // Status reporting every 60 seconds
  setInterval(() => {
    const status = manager.getStatus();
    console.log(`\n📈 Status Report:`);
    console.log(`   Uptime: ${Math.round(status.manager.uptime / 1000)}s`);
    console.log(`   Running modules: ${status.manager.runningModules}/${status.manager.moduleCount}`);

    // Show alert counts
    const alertModule = manager.getModule('alerts');
    if (alertModule) {
      const counts = alertModule.getAlertCounts();
      const totalAlerts = Object.values(counts).reduce((sum, count) => sum + count, 0);
      console.log(`   Total alerts: ${totalAlerts} (🔴${counts.CRITICAL} 🟠${counts.HIGH} 🟡${counts.MEDIUM} 🔵${counts.LOW})`);
    }
    
    // Show service states
    const httpModule = manager.getModule('httpHealth');
    if (httpModule) {
      const states = httpModule.getServiceStates();
      console.log(`   HTTP Services:`);
      Object.entries(states).forEach(([name, state]) => {
        const statusIcon = state.status === 'healthy' ? '🟢' :
                          state.status === 'unhealthy' ? '🟠' : '🔴';
        console.log(`     ${statusIcon} ${name}: ${state.status} (${state.responseTime}ms)`);
      });
    }

    // Show WebSocket states
    const wsModule = manager.getModule('webSocket');
    if (wsModule) {
      const states = wsModule.getConnectionStates();
      console.log(`   WebSocket Connections:`);
      Object.entries(states).forEach(([name, state]) => {
        const statusIcon = state.status === 'connected' ? '🟢' : '🔴';
        const latency = state.averageLatency ? ` (${Math.round(state.averageLatency)}ms)` : '';
        console.log(`     ${statusIcon} ${state.name}: ${state.status}${latency}`);
      });
    }
  }, 60000);

  // Graceful shutdown
  process.on('SIGINT', async () => {
    console.log('\n\n🔄 Shutting down...');
    await manager.shutdown();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    console.log('\n\n🔄 Shutting down...');
    await manager.shutdown();
    process.exit(0);
  });
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the system
main().catch((error) => {
  console.error('Failed to start monitoring system:', error);
  process.exit(1);
});
