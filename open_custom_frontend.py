#!/usr/bin/env python3

import webbrowser
import time
import requests
import subprocess
import os

def wait_for_service(url, service_name, timeout=60):
    """Wait for a service to become available"""
    print(f"⏳ Waiting for {service_name} to start...")
    
    for i in range(timeout):
        try:
            response = requests.get(url, timeout=3)
            if response.status_code == 200:
                print(f"✅ {service_name} is ready!")
                return True
        except:
            pass
        
        time.sleep(1)
        if i % 10 == 0 and i > 0:
            print(f"   Still waiting for {service_name}... ({i}s)")
    
    print(f"❌ {service_name} failed to start within {timeout} seconds")
    return False

def main():
    print("🚀 Custom Frontend Startup Helper")
    print("=" * 50)
    
    # Check if services are already running
    services = [
        ("http://localhost:8188/", "ComfyUI Backend"),
        ("http://localhost:8000/", "Custom Backend"),
        ("http://localhost:3003/", "Custom Frontend")
    ]
    
    running_services = []
    for url, name in services:
        try:
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                running_services.append(name)
                print(f"✅ {name} is already running")
        except:
            print(f"❌ {name} is not running")
    
    if len(running_services) == 3:
        print(f"\n🎉 All services are running!")
        print(f"🌐 Opening Custom Frontend: http://localhost:3003")
        webbrowser.open("http://localhost:3003")
        return
    
    print(f"\n📋 Instructions:")
    print(f"1. Run: cd g:\\comfyui_Front")
    print(f"2. Run: start_with_cuda.bat")
    print(f"3. Wait for all services to start")
    print(f"4. Use Custom Frontend: http://localhost:3003")
    print(f"")
    print(f"⚠️  DO NOT USE: http://localhost:8188 (ComfyUI original UI)")
    print(f"✅  USE THIS:   http://localhost:3003 (Custom Frontend)")

if __name__ == "__main__":
    main()
