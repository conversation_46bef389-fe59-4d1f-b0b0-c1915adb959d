@echo off
setlocal enabledelayedexpansion
title ComfyUI Quick Start
echo.
echo 🚀 ComfyUI Custom Frontend - Quick Start
echo ========================================
echo.

REM Check if ports are already in use
echo 📍 Checking if services are already running...
netstat -aon | findstr ":8188 " >nul 2>&1
if %errorlevel% == 0 (
    echo ⚠️  Port 8188 (ComfyUI) is already in use!
    echo 💡 Run STOP_ALL_SERVICES.bat first, or use emergency_port_cleanup.bat
    echo.
    set /p cont="Continue anyway? (y/n): "
    if /i not "!cont!"=="y" (
        echo ❌ Startup cancelled.
        pause
        exit /b
    )
)

echo Starting all services (verified working system)...
echo.

REM Start ComfyUI (GPU accelerated, no xformers)
echo 📍 Starting ComfyUI Server...
cd /d "g:\PORT_COMFY_front\ComfyUI_windows_portable"
start "ComfyUI" cmd /k "python_embeded\python.exe -s ComfyUI\main.py --windows-standalone-build --disable-auto-launch --disable-xformers"

REM Wait for ComfyUI to initialize
timeout /t 8 /nobreak >nul

REM Start Backend API
echo 📍 Starting Backend API...
cd /d "g:\comfyui_Front"
start "Backend" cmd /k "call Comfyvenv\Scripts\activate.bat && python backend\main.py"

REM Wait for backend to initialize  
timeout /t 3 /nobreak >nul

REM Start Frontend
echo 📍 Starting Frontend...
cd /d "g:\comfyui_Front\frontend"
start "Frontend" cmd /k "npm run dev"

echo.
echo ✅ All services starting!
echo.
echo 🌐 URLs:
echo    Frontend: http://localhost:3000
echo    Backend:  http://localhost:8000
echo    ComfyUI:  http://localhost:8188
echo.
echo ⏳ Services will be ready in ~30 seconds
echo.

timeout /t 5 /nobreak >nul
start http://localhost:3000

echo Press any key to exit this window...
pause >nul
