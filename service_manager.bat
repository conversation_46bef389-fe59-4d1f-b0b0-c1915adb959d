@echo off
title ComfyUI Service Manager with Error Tracking
color 0B

REM Initialize logging
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_system_start "Service Manager Started" >nul 2>&1

:menu
cls
echo.
echo 🎛️  ComfyUI Service Manager
echo ===========================
echo.
echo 📊 Current Status:
echo ----------------
echo.

REM Check service status with logging
echo 🔍 Checking services...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "SERVICE_MANAGER" "Service status check initiated" >nul 2>&1

tasklist /fi "windowtitle eq ComfyUI Backend" 2>nul | find "cmd.exe" >nul
if not errorlevel 1 (
    echo   🟢 ComfyUI Backend: Running
) else (
    echo   🔴 ComfyUI Backend: Stopped
)

tasklist /fi "windowtitle eq Frontend Dev Server" 2>nul | find "node.exe" >nul
if not errorlevel 1 (
    echo   🟢 Frontend Server: Running
) else (
    echo   🔴 Frontend Server: Stopped
)

echo.
echo 🎯 Available Actions:
echo --------------------
echo   1. 🚀 Start Complete System
echo   2. 🛑 Stop All Services
echo   3. ⚡ Quick Start (Fast Launch)
echo   4. 🔄 Restart All Services
echo   5. 🔍 Check Port Status
echo   6. 🧹 Emergency Port Cleanup
echo   7. 📊 Check System Errors
echo   8. 🔧 Individual Service Control
echo   9. 🛠️  System Utilities
echo   0. ❌ Exit Manager
echo.

set /p choice="Select option (0-9): "

python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "SERVICE_MANAGER" "User selected option %choice%" >nul 2>&1

if "%choice%"=="1" goto start_complete
if "%choice%"=="2" goto stop_all
if "%choice%"=="3" goto quick_start
if "%choice%"=="4" goto restart_all
if "%choice%"=="5" goto check_ports
if "%choice%"=="6" goto emergency_cleanup
if "%choice%"=="7" goto check_errors
if "%choice%"=="8" goto individual_control
if "%choice%"=="9" goto utilities
if "%choice%"=="0" goto exit_manager

echo ❌ Invalid choice. Please try again.
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_warning "SERVICE_MANAGER" "Invalid menu choice: %choice%" >nul 2>&1
timeout /t 2 /nobreak >nul
goto menu

:start_complete
cls
echo 🚀 Starting Complete System...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "SERVICE_MANAGER" "Starting complete system via manager"
call START_COMPLETE_SYSTEM.bat
pause
goto menu

:stop_all
cls
echo 🛑 Stopping All Services...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "SERVICE_MANAGER" "Stopping all services via manager"
call STOP_ALL_SERVICES.bat
pause
goto menu

:quick_start
cls
echo ⚡ Quick Start...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "SERVICE_MANAGER" "Quick start initiated via manager"
call quick_start.bat
pause
goto menu

:restart_all
cls
echo 🔄 Restarting All Services...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "SERVICE_MANAGER" "Restarting all services via manager"
echo.
echo 🛑 Stopping services first...
call STOP_ALL_SERVICES.bat
echo.
echo ⏳ Waiting for clean shutdown...
timeout /t 3 /nobreak >nul
echo.
echo 🚀 Starting services...
call START_COMPLETE_SYSTEM.bat
pause
goto menu

:check_ports
cls
echo 🔍 Checking Port Status...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "SERVICE_MANAGER" "Port status check via manager"
call check_ports.bat
pause
goto menu

:emergency_cleanup
cls
echo 🧹 Emergency Port Cleanup...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_warning "SERVICE_MANAGER" "Emergency port cleanup initiated"
call emergency_port_cleanup.bat
pause
goto menu

:check_errors
cls
echo 📊 Checking System Errors...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "SERVICE_MANAGER" "Error log check via manager"
call check_errors_for_claude.bat
goto menu

:individual_control
cls
echo.
echo 🔧 Individual Service Control
echo =============================
echo.
echo   1. 🔄 Restart Backend Only
echo   2. 🔄 Restart Frontend Only
echo   3. 🛑 Stop Backend Only
echo   4. 🛑 Stop Frontend Only
echo   5. 🚀 Start Backend Only
echo   6. 🚀 Start Frontend Only
echo   9. ⬅️  Back to Main Menu
echo.

set /p subchoice="Select service action (1-6, 9): "

python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "SERVICE_MANAGER" "Individual service control: option %subchoice%" >nul 2>&1

if "%subchoice%"=="1" goto restart_backend
if "%subchoice%"=="2" goto restart_frontend
if "%subchoice%"=="3" goto stop_backend
if "%subchoice%"=="4" goto stop_frontend
if "%subchoice%"=="5" goto start_backend
if "%subchoice%"=="6" goto start_frontend
if "%subchoice%"=="9" goto menu

echo ❌ Invalid choice.
timeout /t 2 /nobreak >nul
goto individual_control

:restart_backend
echo 🔄 Restarting Backend...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "BACKEND" "Backend restart initiated"
taskkill /f /fi "windowtitle eq ComfyUI Backend" >nul 2>&1
timeout /t 2 /nobreak >nul
cd /d "g:\comfyui_Front"
start "ComfyUI Backend" cmd /k "python backend\main.py"
echo ✅ Backend restarted
pause
goto individual_control

:restart_frontend
echo 🔄 Restarting Frontend...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "FRONTEND" "Frontend restart initiated"
taskkill /f /fi "windowtitle eq Frontend Dev Server" >nul 2>&1
timeout /t 2 /nobreak >nul
cd /d "g:\comfyui_Front\frontend"
start "Frontend Dev Server" cmd /k "npm run dev"
echo ✅ Frontend restarted
pause
goto individual_control

:stop_backend
echo 🛑 Stopping Backend...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "BACKEND" "Backend stop initiated"
taskkill /f /fi "windowtitle eq ComfyUI Backend" >nul 2>&1
echo ✅ Backend stopped
pause
goto individual_control

:stop_frontend
echo 🛑 Stopping Frontend...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "FRONTEND" "Frontend stop initiated"
taskkill /f /fi "windowtitle eq Frontend Dev Server" >nul 2>&1
echo ✅ Frontend stopped
pause
goto individual_control

:start_backend
echo 🚀 Starting Backend...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "BACKEND" "Backend start initiated"
cd /d "g:\comfyui_Front"
start "ComfyUI Backend" cmd /k "python backend\main.py"
echo ✅ Backend started
pause
goto individual_control

:start_frontend
echo 🚀 Starting Frontend...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "FRONTEND" "Frontend start initiated"
cd /d "g:\comfyui_Front\frontend"
start "Frontend Dev Server" cmd /k "npm run dev"
echo ✅ Frontend started
pause
goto individual_control

:utilities
cls
echo.
echo 🛠️  System Utilities
echo ===================
echo.
echo   1. 📊 View All Log Files
echo   2. 🧹 Clear All Logs
echo   3. 🔍 Test Backend Endpoint
echo   4. 🌐 Open Frontend in Browser
echo   5. 📋 System Information
echo   9. ⬅️  Back to Main Menu
echo.

set /p utilchoice="Select utility (1-5, 9): "

if "%utilchoice%"=="1" goto view_logs
if "%utilchoice%"=="2" goto clear_logs
if "%utilchoice%"=="3" goto test_backend
if "%utilchoice%"=="4" goto open_frontend
if "%utilchoice%"=="5" goto system_info
if "%utilchoice%"=="9" goto menu

echo ❌ Invalid choice.
timeout /t 2 /nobreak >nul
goto utilities

:view_logs
echo 📊 Viewing Log Files...
cd /d "C:\temp\comfyui_workspace\logs"
dir *.log *.json
echo.
echo Content preview:
echo ================
type claude_errors.json 2>nul || echo No errors found
pause
goto utilities

:clear_logs
echo 🧹 Clearing Log Files...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_warning "SERVICE_MANAGER" "Log files cleared by user"
del /q "C:\temp\comfyui_workspace\logs\*.log" 2>nul
del /q "C:\temp\comfyui_workspace\logs\*.json" 2>nul
echo ✅ Logs cleared
pause
goto utilities

:test_backend
echo 🔍 Testing Backend Endpoint...
cd /d "g:\comfyui_Front"
python test_backend_endpoint.py
pause
goto utilities

:open_frontend
echo 🌐 Opening Frontend...
start "" "http://localhost:5173"
echo ✅ Frontend opened in browser
pause
goto utilities

:system_info
echo 📋 System Information...
echo.
echo Python Version:
python --version
echo.
echo Node Version:
node --version 2>nul || echo Node.js not found
echo.
echo Current Directory:
cd
echo.
echo Port Status:
netstat -an | findstr "5173\|8188"
pause
goto utilities

:exit_manager
cls
echo.
echo 👋 Exiting Service Manager...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "SERVICE_MANAGER" "Service manager session ended"
echo.
echo Thank you for using ComfyUI Service Manager!
echo.
timeout /t 2 /nobreak >nul
exit