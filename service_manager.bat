@echo off
title ComfyUI Service Manager
color 0E

:menu
cls
echo.
echo ========================================================================
echo                      🛠️  ComfyUI SERVICE MANAGER
echo ========================================================================
echo.
echo Current System Status: ✅ FULLY OPERATIONAL (v1.1.0)
echo GPU: RTX 4070 Ti SUPER with PyTorch 2.8.0+cu128
echo.
echo Select an action:
echo.
echo   1️⃣  Start Complete System (Full startup with verification)
echo   2️⃣  Quick Start (Fast startup for daily use)
echo   3️⃣  Stop All Services
echo   4️⃣  Check System Status
echo   5️⃣  Check Port Status
echo   6️⃣  Open Service URLs
echo   7️⃣  View Service Logs
echo   8️⃣  Restart Individual Service
echo   9️⃣  System Utilities
echo   0️⃣  Exit
echo.
set /p choice="Enter your choice (0-9): "

if "%choice%"=="1" goto :full_start
if "%choice%"=="2" goto :quick_start
if "%choice%"=="3" goto :stop_services
if "%choice%"=="4" goto :check_status
if "%choice%"=="5" goto :check_ports
if "%choice%"=="6" goto :open_urls
if "%choice%"=="7" goto :view_logs
if "%choice%"=="8" goto :restart_service
if "%choice%"=="9" goto :utilities
if "%choice%"=="0" goto :exit

echo Invalid choice. Please try again.
pause
goto :menu

:full_start
cls
echo 🚀 Starting Complete System with Full Verification...
echo.
call START_COMPLETE_SYSTEM.bat
pause
goto :menu

:quick_start
cls
echo ⚡ Quick Starting All Services...
echo.
call quick_start.bat
pause
goto :menu

:stop_services
cls
echo 🛑 Stopping All Services...
echo.
call STOP_ALL_SERVICES.bat
pause
goto :menu

:check_status
cls
echo 🔍 Checking System Status...
echo.
call check_system.bat
pause
goto :menu

:check_ports
cls
echo 🔍 Checking Port Status...
echo.
call check_ports.bat
pause
goto :menu

:open_urls
cls
echo 🌐 Opening Service URLs...
echo.
echo Opening Frontend...
start http://localhost:3000
timeout /t 2 /nobreak >nul

echo Opening Backend API...
start http://localhost:8000
timeout /t 2 /nobreak >nul

echo Opening ComfyUI...
start http://localhost:8188
timeout /t 2 /nobreak >nul

echo ✅ All URLs opened in browser.
pause
goto :menu

:view_logs
cls
echo 📋 Service Logs - Check the individual service windows for detailed logs
echo.
echo Service Windows:
echo - ComfyUI Server: Check command window titled "ComfyUI"
echo - Backend API: Check command window titled "Backend"  
echo - Frontend: Check command window titled "Frontend"
echo.
echo 💡 Tip: Service windows remain open to show real-time logs
pause
goto :menu

:restart_service
cls
echo 🔄 Restart Individual Service
echo.
echo Which service would you like to restart?
echo   1. ComfyUI Server
echo   2. Backend API
echo   3. Frontend
echo   4. Back to main menu
echo.
set /p svc_choice="Enter choice (1-4): "

if "%svc_choice%"=="1" (
    echo Restarting ComfyUI Server...
    taskkill /f /im python.exe /fi "WINDOWTITLE eq ComfyUI*" 2>nul
    timeout /t 2 /nobreak >nul
    cd /d "g:\PORT_COMFY_front\ComfyUI_windows_portable"
    start "ComfyUI" cmd /k "python_embeded\python.exe -s ComfyUI\main.py --windows-standalone-build --disable-auto-launch --disable-xformers"
    echo ✅ ComfyUI Server restarted
)
if "%svc_choice%"=="2" (
    echo Restarting Backend API...
    taskkill /f /im python.exe /fi "WINDOWTITLE eq Backend*" 2>nul
    timeout /t 2 /nobreak >nul
    cd /d "g:\comfyui_Front"
    start "Backend" cmd /k "call Comfyvenv\Scripts\activate.bat && python backend\main.py"
    echo ✅ Backend API restarted
)
if "%svc_choice%"=="3" (
    echo Restarting Frontend...
    taskkill /f /im node.exe /fi "WINDOWTITLE eq Frontend*" 2>nul
    timeout /t 2 /nobreak >nul
    cd /d "g:\comfyui_Front\frontend"
    start "Frontend" cmd /k "npm run dev"
    echo ✅ Frontend restarted
)
if "%svc_choice%"=="4" goto :menu

pause
goto :menu

:utilities
cls
echo 🛠️  System Utilities
echo.
echo   1. Create Backup
echo   2. Initialize Git Repository
echo   3. View Documentation
echo   4. Check System Requirements
echo   5. Back to main menu
echo.
set /p util_choice="Enter choice (1-5): "

if "%util_choice%"=="1" (
    echo Creating backup...
    call create_backup.bat
)
if "%util_choice%"=="2" (
    echo Initializing Git...
    call init_git.bat
)
if "%util_choice%"=="3" (
    echo Opening documentation...
    start README.md
    start SETUP_CONFIRMED.md
    start CHANGELOG.md
)
if "%util_choice%"=="4" (
    echo Checking system requirements...
    call check_system.bat
)
if "%util_choice%"=="5" goto :menu

pause
goto :menu

:exit
cls
echo.
echo 👋 Thank you for using ComfyUI Custom Frontend!
echo.
echo 📊 System Status: All fixes applied and verified
echo 🎯 Generation Pipeline: Fully operational with async polling
echo 💻 GPU Acceleration: RTX 4070 Ti SUPER ready
echo.
echo For support, check:
echo - SETUP_CONFIRMED.md (current status)
echo - CHANGELOG.md (recent fixes)  
echo - README.md (usage guide)
echo.
timeout /t 3 /nobreak >nul
exit
