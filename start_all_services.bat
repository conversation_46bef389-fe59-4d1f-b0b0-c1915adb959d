@echo off
echo ===============================================
echo  ComfyUI Complete System Startup (Working)
echo ===============================================
echo.
:: [1] Starting Ollama Server
echo [1/5] Starting Ollama Server...
start "Ollama" cmd /c "ollama serve"
echo Waiting 10 seconds for Ollama...
timeout /t 10 /nobreak >nul
echo.
:: [2] Starting Model Validation Service
echo [2/5] Starting Model Validation Service...
cd /d "g:\comfyui_Front"
start "Model Validation" cmd /c "Comfyvenv\Scripts\activate.bat && python model_validation_service.py"
echo Waiting 5 seconds for validation service...
timeout /t 5 /nobreak >nul
echo.

:: Check if we're in the correct directory
if not exist "ComfyUI_windows_portable" (
    echo Error: Please run this script from the PORT_COMFY_front directory
    echo Current directory: %CD%
    pause
    exit /b 1
)

echo [3/5] Starting ComfyUI Backend...
cd /d "ComfyUI_windows_portable"

:: Start ComfyUI backend in new window with correct parameters
start "ComfyUI Backend (Port 8188)" python_embeded\python.exe -s ComfyUI\main.py --highvram --use-split-cross-attention --fp16-vae --dont-upcast-attention --listen 0.0.0.0 --port 8188

echo ComfyUI Backend starting... (Port 8188)
echo Waiting 20 seconds for ComfyUI to initialize...
timeout /t 20 /nobreak

echo.
echo [4/5] Starting FastAPI Middleware...
cd /d "..\comfyui_Front\backend"

:: Start FastAPI backend in new window
start "FastAPI Middleware (Port 8000)" cmd /k "Backvenv\Scripts\activate.bat && python main.py"

echo FastAPI Middleware starting... (Port 8000)
echo Waiting 10 seconds for FastAPI to initialize...
timeout /t 10 /nobreak

echo.
echo [5/5] Starting Frontend Development Server...
cd /d "..\frontend"

:: Start frontend in new window
start "Frontend (Port 3003)" cmd /k "npm run dev"

echo Frontend starting... (Port 3003)
echo Waiting 8 seconds for frontend to initialize...
timeout /t 8 /nobreak

echo.
echo ===============================================
echo  System Status Check
echo ===============================================
echo.

:: Test connections
echo Testing ComfyUI connection...
curl -s http://localhost:8188/system_stats >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ ComfyUI Backend    : http://localhost:8188 [RUNNING]
) else (
    echo ❌ ComfyUI Backend    : http://localhost:8188 [FAILED - May need more time]
)

echo Testing FastAPI connection...
curl -s http://localhost:8000/ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ FastAPI Middleware : http://localhost:8000 [RUNNING]
) else (
    echo ❌ FastAPI Middleware : http://localhost:8000 [FAILED - May need more time]
)

echo Testing Frontend connection...
curl -s http://localhost:3003 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Frontend UI        : http://localhost:3003 [RUNNING]
) else (
    echo ❌ Frontend UI        : http://localhost:3003 [FAILED - May need more time]
)

echo.
echo ===============================================
echo  Available Models Found in ComfyUI
echo ===============================================
echo.
echo Checkpoints:
echo  - flux1-kontext-dev.safetensors (FLUX)
echo  - pixelwave_flux1_dev_fp8_03.safetensors (FLUX)
echo  - Juggernaut-XL_v9_RunDiffusionPhoto_v2.safetensors (SDXL)
echo  - SD1dreamshaper_8.safetensors (SD1.5)
echo  - Sd1diffusionBrushEverythingSFWNSFWAll_v10.safetensors (SD1.5)
echo.
echo VAEs:
echo  - Flux_vae.safetensors
echo  - sdxl_vae.safetensors
echo  - wan_2.1_vae.safetensors
echo.
echo Text Encoders:
echo  - t5xxl_fp16.safetensors
echo  - clip_l.safetensors
echo.
echo ===============================================
echo  Ready for Image Generation!
echo ===============================================
echo.
echo Open your browser and navigate to:
echo http://localhost:3003
echo.
echo The system should show:
echo - Backend: Connected
echo - ComfyUI: Ready
echo - Models loading in dropdown
echo.
echo Press any key to open the frontend...
pause > nul

:: Open frontend in default browser
start "" "http://localhost:3003"

echo.
echo System is ready! Keep this window open to monitor services.
echo To stop all services, close all opened terminal windows.
echo.
echo For text-to-image generation:
echo 1. Select a model (flux1-kontext-dev.safetensors recommended)
echo 2. Enter a prompt
echo 3. Click "Generate Image"
echo.
pause