@echo off
echo.
echo 🚀 Starting ComfyUI (Simple Mode)
echo.
echo This script starts ComfyUI without xformers to avoid compatibility issues
echo while still maintaining CUDA GPU acceleration.
echo.
echo 📍 Starting ComfyUI with CUDA but no xformers...
cd /d "g:\PORT_COMFY_front\ComfyUI_windows_portable"
start "ComfyUI" cmd /k "python_embeded\python.exe -s ComfyUI\main.py --windows-standalone-build --disable-auto-launch --disable-xformers"

echo.
echo ⏳ Waiting 5 seconds for ComfyUI to start...
timeout /t 5 /nobreak >nul

echo.
echo 📍 Starting Backend Server...
cd /d "g:\comfyui_Front"
call Comfyvenv\Scripts\activate.bat
start "Backend" cmd /k "python backend/main.py"

echo.
echo ⏳ Waiting 3 seconds for backend to start...
timeout /t 3 /nobreak >nul

echo.
echo 📍 Starting Frontend Development Server...
start "Frontend" cmd /k "cd frontend && npm run dev"

echo.
echo ✅ All services started!
echo.
echo Services running:
echo - ComfyUI: http://localhost:8188 (no xformers, CUDA enabled)
echo - Backend: http://localhost:8000
echo - Frontend: http://localhost:3000
echo.
echo Press any key to exit...
pause >nul
