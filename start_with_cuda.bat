@echo off
echo 🔧 Starting ComfyUI and Backend with CUDA Support
echo ====================================================

echo.
echo 📍 Step 1: Starting ComfyUI with GPU support (xformers disabled for stability)...
cd /d "g:\PORT_COMFY_front\ComfyUI_windows_portable"
start "ComfyUI" cmd /k "python_embeded\python.exe -s ComfyUI\main.py --windows-standalone-build --disable-auto-launch --disable-xformers"

echo.
echo ⏳ Waiting 15 seconds for ComfyUI to start...
timeout /t 15 /nobreak > nul

echo.
echo 📍 Step 2: Testing ComfyUI connection...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8188/' -UseBasicParsing -TimeoutSec 5; Write-Host '✅ ComfyUI is running on port 8188 - Status:' $response.StatusCode } catch { Write-Host '❌ ComfyUI not accessible:' $_.Exception.Message }"

echo.
echo 📍 Step 3: Installing PyTorch with CUDA in backend environment...
cd /d "g:\comfyui_Front"
call Backvenv\Scripts\activate.bat
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128

echo.
echo 📍 Step 4: Starting backend server...
cd backend
start "Backend" cmd /k "python main.py"

echo.
echo 🎉 Startup complete!
echo - ComfyUI: http://localhost:8188
echo - Backend: http://localhost:8000  
echo - Frontend: http://localhost:3000
echo.
echo Press any key to exit this startup script...
pause > nul
