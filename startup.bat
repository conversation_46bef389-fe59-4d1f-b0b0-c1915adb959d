@echo off
setlocal enabledelayedexpansion
title ComfyUI Frontend Complete Startup

echo ================================================================
echo   ComfyUI Frontend Complete System Startup
echo   Enhanced Image Generation Platform
echo ================================================================
echo.

:: Set color for better visibility
color 0A

:: Store the original directory
set "ORIGINAL_DIR=%CD%"

:: Function to print section headers
set "SECTION=1"

:check_directory
echo [%SECTION%/8] Checking Project Structure...
set /a SECTION+=1

:: Check if we're in the correct directory by looking for key files
if exist "frontend\package.json" (
    echo ✓ Frontend directory found
) else (
    echo ❌ Frontend directory not found!
    echo Please run this script from the comfyui_Front directory
    echo Current directory: %CD%
    pause
    exit /b 1
)

if exist "backend\main.py" (
    echo ✓ Backend directory found
) else (
    echo ❌ Backend directory not found!
    echo Please ensure backend is present
    pause
    exit /b 1
)

echo.

:check_dependencies
echo [%SECTION%/8] Checking Dependencies...
set /a SECTION+=1

:: Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed or not in PATH!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do echo ✓ Node.js: %%i
)

:: Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Python not found in PATH, trying alternative locations...
    if exist "Comfyvenv\Scripts\python.exe" (
        echo ✓ Python found in virtual environment
        set "PYTHON_PATH=Comfyvenv\Scripts\python.exe"
    ) else (
        echo ❌ Python not found! Some features may not work.
        set "PYTHON_PATH=python"
    )
) else (
    for /f "tokens=*" %%i in ('python --version') do echo ✓ Python: %%i
    set "PYTHON_PATH=python"
)

echo.

:start_comfyui
echo [%SECTION%/8] Starting ComfyUI Backend...
set /a SECTION+=1

:: Check for ComfyUI in multiple possible locations
set "COMFYUI_FOUND=0"

:: Check PORT_COMFY_front location
if exist "..\PORT_COMFY_front\ComfyUI_windows_portable\run_nvidia_gpu_optimized.bat" (
    echo ✓ Found ComfyUI Portable in PORT_COMFY_front
    cd /d "..\PORT_COMFY_front\ComfyUI_windows_portable"
    start "ComfyUI Backend (Port 8188)" run_nvidia_gpu_optimized.bat
    set "COMFYUI_FOUND=1"
    cd /d "%ORIGINAL_DIR%"
) else if exist "L:\ComfyUI\main.py" (
    echo ✓ Found ComfyUI at L:\ComfyUI
    start "ComfyUI Backend (Port 8188)" cmd /k "cd /d L:\ComfyUI && python main.py --listen 0.0.0.0 --port 8188"
    set "COMFYUI_FOUND=1"
) else (
    echo ⚠️ ComfyUI not found in expected locations
    echo ComfyUI will need to be started manually on port 8188
    echo Expected locations:
    echo   - ..\PORT_COMFY_front\ComfyUI_windows_portable\
    echo   - L:\ComfyUI\
)

if %COMFYUI_FOUND%==1 (
    echo ✓ ComfyUI starting on port 8188
    echo Waiting 10 seconds for ComfyUI to initialize...
    timeout /t 10 /nobreak >nul
) else (
    echo Press any key to continue without ComfyUI...
    pause >nul
)

echo.

:start_ollama
echo [%SECTION%/8] Starting Ollama Service...
set /a SECTION+=1

:: Check if Ollama is installed
ollama --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Ollama not found. Prompt enhancement will not be available.
    echo To install Ollama, visit: https://ollama.ai/
) else (
    for /f "tokens=*" %%i in ('ollama --version') do echo ✓ Ollama: %%i
    
    :: Check if Ollama service is already running
    curl -s http://localhost:11434/api/tags >nul 2>&1
    if errorlevel 1 (
        echo Starting Ollama service...
        start "Ollama Service (Port 11434)" cmd /k "ollama serve"
        timeout /t 3 /nobreak >nul
    ) else (
        echo ✓ Ollama service already running
    )
    
    :: Check for required model
    echo Checking for llama3.2 model...
    ollama list | findstr "llama3.2" >nul 2>&1
    if errorlevel 1 (
        echo ⚠️ llama3.2 model not found
        echo You can download it with: ollama pull llama3.2
    ) else (
        echo ✓ llama3.2 model available
    )
)

echo.

:start_backend
echo [%SECTION%/8] Starting FastAPI Backend...
set /a SECTION+=1

if exist "backend\main.py" (
    echo Starting custom backend on port 8000...
    start "FastAPI Backend (Port 8000)" cmd /k "cd /d %ORIGINAL_DIR%\backend && %PYTHON_PATH% main.py"
    echo Waiting 5 seconds for backend to start...
    timeout /t 5 /nobreak >nul
) else (
    echo ❌ Backend main.py not found!
    pause
    exit /b 1
)

echo.

:install_frontend_deps
echo [%SECTION%/8] Installing Frontend Dependencies...
set /a SECTION+=1

cd frontend
if not exist "node_modules" (
    echo Installing Node.js dependencies...
    call npm install
    if errorlevel 1 (
        echo ❌ Failed to install dependencies!
        echo Please check your internet connection and try again.
        pause
        exit /b 1
    )
) else (
    echo ✓ Dependencies already installed
    echo Checking for updates...
    call npm install >nul 2>&1
)

echo.

:test_connections
echo [%SECTION%/8] Testing Service Connections...
set /a SECTION+=1

:: Test ComfyUI
echo Testing ComfyUI (Port 8188)...
curl -s --connect-timeout 3 http://localhost:8188/system_stats >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ ComfyUI responding
) else (
    echo ⚠️ ComfyUI not responding - may still be starting
)

:: Test Ollama
echo Testing Ollama (Port 11434)...
curl -s --connect-timeout 3 http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Ollama responding
) else (
    echo ⚠️ Ollama not responding
)

:: Test Backend
echo Testing FastAPI Backend (Port 8000)...
curl -s --connect-timeout 3 http://localhost:8000/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ FastAPI Backend responding
) else (
    echo ⚠️ Backend not responding - may still be starting
)

echo.

:start_frontend
echo [%SECTION%/8] Starting Frontend Development Server...
set /a SECTION+=1

echo ================================================================
echo   🚀 SYSTEM STARTUP COMPLETE 🚀
echo ================================================================
echo.
echo   Service Status:
echo   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo   📱 Frontend:     http://localhost:3003
echo   🤖 ComfyUI:      http://localhost:8188
echo   🧠 Ollama:       http://localhost:11434  
echo   ⚡ Backend API:  http://localhost:8000
echo   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.
echo   🎨 Enhanced Image Generation Platform Ready!
echo   
echo   Features Available:
echo   • Enhanced UI with dual viewing modes
echo   • Professional image display with focus view
echo   • Test mode for UI development
echo   • Comprehensive error handling
echo.
echo   The application will open in your browser automatically...
echo   Press Ctrl+C to stop the development server.
echo.

:: Open browser after a short delay - Use Custom Frontend
timeout /t 3 /nobreak >nul
echo.
echo ================================================================
echo   🎉 OPENING CUSTOM FRONTEND (NOT COMFYUI ORIGINAL UI)
echo   URL: http://localhost:3003
echo ================================================================
start "" "http://localhost:3003"

:: Start the development server
echo Starting Next.js development server...
call npm run dev

:: Cleanup message when server stops
echo.
echo ================================================================
echo   Development server stopped.
echo   To restart: run startup.bat again
echo ================================================================
pause

:end
cd /d "%ORIGINAL_DIR%"
endlocal
