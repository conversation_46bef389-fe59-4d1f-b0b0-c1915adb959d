@echo off
setlocal enabledelayedexpansion
title ComfyUI - Stop All Services
color 0C
echo.
echo ========================================================================
echo                    🛑 STOP ALL COMFYUI SERVICES
echo ========================================================================
echo.
echo This will terminate all running ComfyUI-related processes:
echo   - ComfyUI Server (Python processes)
echo   - Backend API Server (FastAPI)
echo   - Frontend Development Server (Node.js)
echo   - All associated command windows
echo.

set /p confirm="⚠️  Stop all services? (y/n): "
if /i not "%confirm%"=="y" (
    echo ❌ Operation cancelled.
    goto :end
)

echo.
echo 🛑 Stopping all ComfyUI services...
echo.

REM Stop ComfyUI Server processes
echo 📍 Terminating ComfyUI Server processes...
taskkill /f /im python.exe /fi "WINDOWTITLE eq ComfyUI*" 2>nul
if %errorlevel% == 0 (
    echo   ✅ ComfyUI Server processes terminated
) else (
    echo   ℹ️  No ComfyUI Server processes found
)

REM Stop Backend API processes
echo 📍 Terminating Backend API processes...
taskkill /f /im python.exe /fi "WINDOWTITLE eq Backend*" 2>nul
if %errorlevel% == 0 (
    echo   ✅ Backend API processes terminated
) else (
    echo   ℹ️  No Backend API processes found
)

REM Find and stop Python processes running from ComfyUI directories
echo 📍 Terminating ComfyUI Python processes by path...
setlocal enabledelayedexpansion
for /f "tokens=2 delims=," %%i in ('tasklist /fi "imagename eq python.exe" /fo csv /nh 2^>nul') do (
    set "pid=%%~i"
    for /f "tokens=*" %%j in ('wmic process where "processid=!pid!" get commandline /value 2^>nul ^| findstr "CommandLine"') do (
        echo %%j | findstr /i "ComfyUI" >nul
        if !errorlevel! == 0 (
            taskkill /f /pid !pid! 2>nul
            echo   ✅ Terminated ComfyUI Python process (PID: !pid!)
        )
    )
)

REM Kill any processes using ComfyUI ports
echo 📍 Freeing ComfyUI ports...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":8188 " 2^>nul') do (
    set "pid=%%a"
    if "!pid!" neq "0" (
        taskkill /f /pid !pid! 2>nul
        echo   ✅ Freed port 8188 (PID: !pid!)
    )
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":8000 " 2^>nul') do (
    set "pid=%%a"
    if "!pid!" neq "0" (
        taskkill /f /pid !pid! 2>nul
        echo   ✅ Freed port 8000 (PID: !pid!)
    )
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":3000 " 2^>nul') do (
    set "pid=%%a"
    if "!pid!" neq "0" (
        taskkill /f /pid !pid! 2>nul
        echo   ✅ Freed port 3000 (PID: !pid!)
    )
)

REM Stop Frontend Node.js processes
echo 📍 Terminating Frontend Node.js processes...
taskkill /f /im node.exe /fi "WINDOWTITLE eq Frontend*" 2>nul
if %errorlevel% == 0 (
    echo   ✅ Frontend Node.js processes terminated
) else (
    echo   ℹ️  No Frontend Node.js processes found
)

REM Stop Node processes running from frontend directory
echo 📍 Terminating Node.js processes by path...
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq node.exe" /fo csv ^| findstr "node.exe"') do (
    set "pid=%%i"
    set "pid=!pid:"=!"
    for /f "tokens=*" %%j in ('wmic process where "processid=!pid!" get commandline /value 2^>nul ^| findstr "CommandLine"') do (
        echo %%j | findstr /i "comfyui.*frontend" >nul
        if !errorlevel! == 0 (
            taskkill /f /pid !pid! 2>nul
            echo   ✅ Terminated Frontend Node.js process (PID: !pid!)
        )
    )
)

REM Stop service command windows
echo 📍 Closing service command windows...
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq ComfyUI*" 2>nul
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq Backend*" 2>nul
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq Frontend*" 2>nul

REM Additional cleanup - stop any remaining processes that might be related
echo 📍 Additional cleanup...

REM Stop any Python processes running main.py from ComfyUI
wmic process where "name='python.exe' and commandline like '%%main.py%%'" delete 2>nul

REM Stop any uvicorn processes (FastAPI server)
taskkill /f /im python.exe /fi "WINDOWTITLE eq *uvicorn*" 2>nul

REM Stop any npm/npx processes from frontend
taskkill /f /im node.exe /fi "WINDOWTITLE eq *npm*" 2>nul
taskkill /f /im node.exe /fi "WINDOWTITLE eq *npx*" 2>nul

echo.
echo ========================================================================
echo                           ✅ CLEANUP COMPLETE
echo ========================================================================
echo.
echo 🛑 All ComfyUI services have been stopped
echo.
echo Services terminated:
echo   ✅ ComfyUI Server (python processes)
echo   ✅ Backend API Server (FastAPI/uvicorn)
echo   ✅ Frontend Development Server (Node.js/npm)
echo   ✅ Associated command windows
echo.
echo 📊 Port Status:
echo   - Port 8188 (ComfyUI): Now available
echo   - Port 8000 (Backend): Now available
echo   - Port 3000 (Frontend): Now available
echo.
echo 💡 To restart services, use:
echo   - service_manager.bat (recommended)
echo   - START_COMPLETE_SYSTEM.bat (full verification)
echo   - quick_start.bat (fast startup)
echo.

:end
echo Press any key to exit...
pause >nul
exit /b
