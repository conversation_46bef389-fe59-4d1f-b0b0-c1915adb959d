@echo off
title Stop ComfyUI - Quick
echo.
echo 🛑 Stopping all ComfyUI services...
echo.

REM Kill all Python processes with ComfyUI or backend in window title
taskkill /f /im python.exe /fi "WINDOWTITLE eq ComfyUI*" 2>nul
taskkill /f /im python.exe /fi "WINDOWTITLE eq Backend*" 2>nul
taskkill /f /im python.exe /fi "WINDOWTITLE eq *uvicorn*" 2>nul

REM Kill all Node.js processes with Frontend in window title
taskkill /f /im node.exe /fi "WINDOWTITLE eq Frontend*" 2>nul
taskkill /f /im node.exe /fi "WINDOWTITLE eq *npm*" 2>nul

REM Kill command windows
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq ComfyUI*" 2>nul
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq Backend*" 2>nul
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq Frontend*" 2>nul

REM Additional cleanup - kill processes by executable path
taskkill /f /im python.exe 2>nul | findstr /i "comfyui"
taskkill /f /im node.exe 2>nul | findstr /i "frontend"

echo.
echo ✅ All ComfyUI services stopped
echo.
echo Ports 3000, 8000, and 8188 are now available
echo.
timeout /t 3 /nobreak >nul
