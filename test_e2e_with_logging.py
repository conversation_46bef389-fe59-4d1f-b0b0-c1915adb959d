#!/usr/bin/env python3
"""
Enhanced End-to-End Test with Centralized Logging
Tests the complete ComfyUI pipeline and logs results for Claude accessibility
"""

import sys
import os
import json
import requests
import time
from pathlib import Path

# Add the logging directory to path
sys.path.append(r"C:\temp\comfyui_workspace\logs")

try:
    from centralized_logger import log_info, log_error, log_warning, log_critical
    LOGGING_AVAILABLE = True
except ImportError:
    print("⚠️ Centralized logging not available, using print fallback")
    LOGGING_AVAILABLE = False
    
    def log_info(component, message):
        print(f"[INFO] {component}: {message}")
    
    def log_error(component, message):
        print(f"[ERROR] {component}: {message}")
    
    def log_warning(component, message):
        print(f"[WARNING] {component}: {message}")
    
    def log_critical(component, message):
        print(f"[CRITICAL] {component}: {message}")

class ComfyUITester:
    def __init__(self):
        self.backend_url = "http://localhost:8188"
        self.frontend_url = "http://localhost:5173"
        self.test_results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "tests": {},
            "overall_status": "UNKNOWN"
        }
        
    def test_backend_connectivity(self):
        """Test if ComfyUI backend is responding"""
        print("🔍 Testing backend connectivity...")
        log_info("E2E_TEST", "Starting backend connectivity test")
        
        try:
            response = requests.get(f"{self.backend_url}/system_stats", timeout=10)
            if response.status_code == 200:
                print("✅ Backend connectivity: PASS")
                log_info("E2E_TEST", "Backend connectivity test PASSED")
                self.test_results["tests"]["backend_connectivity"] = "PASS"
                return True
            else:
                print(f"❌ Backend connectivity: FAIL (Status: {response.status_code})")
                log_error("E2E_TEST", f"Backend connectivity FAILED - Status: {response.status_code}")
                self.test_results["tests"]["backend_connectivity"] = "FAIL"
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ Backend connectivity: FAIL (Error: {e})")
            log_error("E2E_TEST", f"Backend connectivity FAILED - Error: {str(e)}")
            self.test_results["tests"]["backend_connectivity"] = "FAIL"
            return False
    
    def test_frontend_availability(self):
        """Test if frontend is serving content"""
        print("🔍 Testing frontend availability...")
        log_info("E2E_TEST", "Starting frontend availability test")
        
        try:
            response = requests.get(self.frontend_url, timeout=10)
            if response.status_code == 200:
                print("✅ Frontend availability: PASS")
                log_info("E2E_TEST", "Frontend availability test PASSED")
                self.test_results["tests"]["frontend_availability"] = "PASS"
                return True
            else:
                print(f"❌ Frontend availability: FAIL (Status: {response.status_code})")
                log_error("E2E_TEST", f"Frontend availability FAILED - Status: {response.status_code}")
                self.test_results["tests"]["frontend_availability"] = "FAIL"
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ Frontend availability: FAIL (Error: {e})")
            log_error("E2E_TEST", f"Frontend availability FAILED - Error: {str(e)}")
            self.test_results["tests"]["frontend_availability"] = "FAIL"
            return False
    
    def test_api_endpoints(self):
        """Test critical API endpoints"""
        print("🔍 Testing API endpoints...")
        log_info("E2E_TEST", "Starting API endpoints test")
        
        endpoints = [
            "/object_info",
            "/queue",
            "/history",
            "/system_stats"
        ]
        
        passed_endpoints = 0
        total_endpoints = len(endpoints)
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"  ✅ {endpoint}: PASS")
                    passed_endpoints += 1
                else:
                    print(f"  ❌ {endpoint}: FAIL (Status: {response.status_code})")
                    log_warning("E2E_TEST", f"API endpoint {endpoint} failed with status {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"  ❌ {endpoint}: FAIL (Error: {e})")
                log_warning("E2E_TEST", f"API endpoint {endpoint} failed with error: {str(e)}")
        
        if passed_endpoints == total_endpoints:
            print("✅ API endpoints: PASS")
            log_info("E2E_TEST", "All API endpoints test PASSED")
            self.test_results["tests"]["api_endpoints"] = "PASS"
            return True
        else:
            print(f"❌ API endpoints: PARTIAL ({passed_endpoints}/{total_endpoints} passed)")
            log_warning("E2E_TEST", f"API endpoints test PARTIAL - {passed_endpoints}/{total_endpoints} passed")
            self.test_results["tests"]["api_endpoints"] = "PARTIAL"
            return False
    
    def test_workflow_generation(self):
        """Test workflow generation capability"""
        print("🔍 Testing workflow generation...")
        log_info("E2E_TEST", "Starting workflow generation test")
        
        # Simple workflow for testing
        test_workflow = {
            "prompt": {
                "1": {
                    "class_type": "CheckpointLoaderSimple",
                    "inputs": {
                        "ckpt_name": "sd_xl_base_1.0.safetensors"
                    }
                }
            }
        }
        
        try:
            response = requests.post(
                f"{self.backend_url}/prompt",
                json=test_workflow,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if "prompt_id" in result:
                    print("✅ Workflow generation: PASS")
                    log_info("E2E_TEST", f"Workflow generation test PASSED - Prompt ID: {result['prompt_id']}")
                    self.test_results["tests"]["workflow_generation"] = "PASS"
                    return True
                else:
                    print("❌ Workflow generation: FAIL (No prompt_id in response)")
                    log_error("E2E_TEST", "Workflow generation FAILED - No prompt_id in response")
                    self.test_results["tests"]["workflow_generation"] = "FAIL"
                    return False
            else:
                print(f"❌ Workflow generation: FAIL (Status: {response.status_code})")
                log_error("E2E_TEST", f"Workflow generation FAILED - Status: {response.status_code}")
                self.test_results["tests"]["workflow_generation"] = "FAIL"
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ Workflow generation: FAIL (Error: {e})")
            log_error("E2E_TEST", f"Workflow generation FAILED - Error: {str(e)}")
            self.test_results["tests"]["workflow_generation"] = "FAIL"
            return False
    
    def test_model_availability(self):
        """Test if required models are available"""
        print("🔍 Testing model availability...")
        log_info("E2E_TEST", "Starting model availability test")
        
        try:
            response = requests.get(f"{self.backend_url}/object_info", timeout=10)
            if response.status_code == 200:
                object_info = response.json()
                
                # Check for checkpoint models
                if "CheckpointLoaderSimple" in object_info:
                    checkpoints = object_info["CheckpointLoaderSimple"]["input"]["required"]["ckpt_name"][0]
                    if len(checkpoints) > 0:
                        print(f"✅ Model availability: PASS ({len(checkpoints)} models found)")
                        log_info("E2E_TEST", f"Model availability test PASSED - {len(checkpoints)} models found")
                        self.test_results["tests"]["model_availability"] = "PASS"
                        return True
                    else:
                        print("❌ Model availability: FAIL (No checkpoint models found)")
                        log_error("E2E_TEST", "Model availability FAILED - No checkpoint models found")
                        self.test_results["tests"]["model_availability"] = "FAIL"
                        return False
                else:
                    print("❌ Model availability: FAIL (CheckpointLoaderSimple not found)")
                    log_error("E2E_TEST", "Model availability FAILED - CheckpointLoaderSimple not found")
                    self.test_results["tests"]["model_availability"] = "FAIL"
                    return False
            else:
                print(f"❌ Model availability: FAIL (Status: {response.status_code})")
                log_error("E2E_TEST", f"Model availability FAILED - Status: {response.status_code}")
                self.test_results["tests"]["model_availability"] = "FAIL"
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ Model availability: FAIL (Error: {e})")
            log_error("E2E_TEST", f"Model availability FAILED - Error: {str(e)}")
            self.test_results["tests"]["model_availability"] = "FAIL"
            return False
    
    def run_all_tests(self):
        """Run complete test suite"""
        print("🧪 ComfyUI End-to-End Test Suite")
        print("=================================")
        log_info("E2E_TEST", "Starting complete end-to-end test suite")
        
        test_methods = [
            self.test_backend_connectivity,
            self.test_frontend_availability,
            self.test_api_endpoints,
            self.test_model_availability,
            self.test_workflow_generation
        ]
        
        passed_tests = 0
        total_tests = len(test_methods)
        
        for test_method in test_methods:
            try:
                if test_method():
                    passed_tests += 1
                print()  # Add spacing between tests
            except Exception as e:
                print(f"❌ {test_method.__name__}: EXCEPTION ({e})")
                log_critical("E2E_TEST", f"{test_method.__name__} failed with exception: {str(e)}")
                print()
        
        # Calculate overall status
        if passed_tests == total_tests:
            self.test_results["overall_status"] = "ALL_PASS"
            status_emoji = "✅"
            status_message = "All tests passed"
        elif passed_tests > 0:
            self.test_results["overall_status"] = "PARTIAL_PASS"
            status_emoji = "⚠️"
            status_message = f"Partial success ({passed_tests}/{total_tests} passed)"
        else:
            self.test_results["overall_status"] = "ALL_FAIL"
            status_emoji = "❌"
            status_message = "All tests failed"
        
        print("📊 Test Results Summary")
        print("======================")
        print(f"{status_emoji} Overall Status: {status_message}")
        print(f"📈 Tests Passed: {passed_tests}/{total_tests}")
        
        # Log final results
        log_info("E2E_TEST", f"Test suite completed - {status_message}")
        
        # Save results to file
        self.save_test_results()
        
        return passed_tests == total_tests
    
    def save_test_results(self):
        """Save test results to file for later analysis"""
        try:
            results_file = Path("test_results_latest.json")
            with open(results_file, 'w') as f:
                json.dump(self.test_results, f, indent=2)
            print(f"📄 Test results saved to: {results_file}")
            log_info("E2E_TEST", f"Test results saved to {results_file}")
        except Exception as e:
            print(f"⚠️ Could not save test results: {e}")
            log_warning("E2E_TEST", f"Could not save test results: {str(e)}")

def main():
    """Main test execution"""
    tester = ComfyUITester()
    success = tester.run_all_tests()
    
    print("\n" + "="*50)
    if success:
        print("🎉 All tests completed successfully!")
        log_info("E2E_TEST", "All tests completed successfully")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Check the logs for details.")
        log_error("E2E_TEST", "Some tests failed during execution")
        sys.exit(1)

if __name__ == "__main__":
    main()
