#!/usr/bin/env python3
"""
End-to-End Generation System Test
Tests the complete generation pipeline with real-time WebSocket communication
"""

import asyncio
import websockets
import json
import requests
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GenerationSystemTester:
    def __init__(self, api_base_url: str = "http://localhost:8000/api/v1", ws_url: str = "ws://localhost:8000/ws"):
        self.api_base_url = api_base_url
        self.ws_url = ws_url
        self.websocket = None
        self.generation_updates = []
        self.test_results = {}
        
    async def connect_websocket(self):
        """Connect to WebSocket and start listening for updates"""
        try:
            logger.info(f"Connecting to WebSocket: {self.ws_url}")
            self.websocket = await websockets.connect(self.ws_url)
            logger.info("WebSocket connected successfully")
            
            # Start listening for messages
            asyncio.create_task(self.listen_websocket())
            
        except Exception as e:
            logger.error(f"Failed to connect to WebSocket: {e}")
            raise
    
    async def listen_websocket(self):
        """Listen for WebSocket messages"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    message_type = data.get("type")
                    
                    if message_type in ["generation_progress", "generation_state", "generation_error", "generation_complete"]:
                        logger.info(f"Received {message_type}: {data}")
                        self.generation_updates.append({
                            "timestamp": datetime.now().isoformat(),
                            "type": message_type,
                            "data": data
                        })
                    
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse WebSocket message: {e}")
                except Exception as e:
                    logger.error(f"Error processing WebSocket message: {e}")
                    
        except websockets.ConnectionClosed:
            logger.warning("WebSocket connection closed")
        except Exception as e:
            logger.error(f"Error in WebSocket listener: {e}")
    
    def test_api_health(self) -> bool:
        """Test API health endpoint"""
        logger.info("Testing API health...")
        try:
            response = requests.get(f"{self.api_base_url.replace('/api/v1', '')}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"API health check passed: {data}")
                self.test_results["api_health"] = {"status": "pass", "data": data}
                return True
            else:
                logger.error(f"API health check failed: {response.status_code}")
                self.test_results["api_health"] = {"status": "fail", "error": f"HTTP {response.status_code}"}
                return False
        except Exception as e:
            logger.error(f"API health check error: {e}")
            self.test_results["api_health"] = {"status": "error", "error": str(e)}
            return False
    
    def start_generation(self) -> Optional[str]:
        """Start a test generation"""
        logger.info("Starting test generation...")
        
        payload = {
            "settings": {
                "prompt": "A beautiful sunset over mountains, digital art, highly detailed, vibrant colors",
                "negative_prompt": "blurry, low quality, distorted, ugly",
                "model": "SDXL-1.0",
                "width": 512,
                "height": 512,
                "steps": 20,
                "cfg_scale": 7.5,
                "seed": -1,
                "sampler": "DPM++ 2M Karras",
                "scheduler": "normal"
            },
            "hardware_config": {
                "use_cpu": False,
                "use_fp16": True,
                "batch_size": 1,
                "memory_management": "auto"
            },
            "metadata": {
                "session_id": f"test_session_{int(time.time())}",
                "user_id": "test_user",
                "tags": ["test", "sunset", "mountains"],
                "description": "End-to-end system test generation"
            }
        }
        
        try:
            response = requests.post(
                f"{self.api_base_url}/generate",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    generation_id = data.get("generation_id")
                    logger.info(f"Generation started successfully: {generation_id}")
                    self.test_results["generation_start"] = {"status": "pass", "generation_id": generation_id, "data": data}
                    return generation_id
                else:
                    logger.error(f"Generation start failed: {data}")
                    self.test_results["generation_start"] = {"status": "fail", "error": data}
                    return None
            else:
                logger.error(f"Generation API error: {response.status_code} - {response.text}")
                self.test_results["generation_start"] = {"status": "error", "error": f"HTTP {response.status_code}"}
                return None
                
        except Exception as e:
            logger.error(f"Generation start error: {e}")
            self.test_results["generation_start"] = {"status": "error", "error": str(e)}
            return None
    
    def get_generation_status(self, generation_id: str) -> Optional[Dict[str, Any]]:
        """Get generation status"""
        try:
            response = requests.get(f"{self.api_base_url}/generation/{generation_id}/status", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Status check failed: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"Status check error: {e}")
            return None
    
    async def monitor_generation(self, generation_id: str, timeout: int = 300) -> bool:
        """Monitor generation progress until completion"""
        logger.info(f"Monitoring generation {generation_id}...")
        
        start_time = time.time()
        last_status = None
        
        while time.time() - start_time < timeout:
            # Check status via API
            status_data = self.get_generation_status(generation_id)
            if status_data:
                status = status_data.get("status")
                progress = status_data.get("progress", 0)
                message = status_data.get("message", "")
                
                if status != last_status:
                    logger.info(f"Generation {generation_id} status: {status} ({progress}%) - {message}")
                    last_status = status
                
                if status in ["COMPLETED", "FAILED", "CANCELLED"]:
                    logger.info(f"Generation {generation_id} finished with status: {status}")
                    self.test_results["generation_monitor"] = {
                        "status": "pass" if status == "COMPLETED" else "fail",
                        "final_status": status,
                        "final_data": status_data
                    }
                    return status == "COMPLETED"
            
            await asyncio.sleep(2)
        
        logger.error(f"Generation {generation_id} monitoring timed out")
        self.test_results["generation_monitor"] = {"status": "timeout", "timeout_seconds": timeout}
        return False
    
    def analyze_websocket_updates(self, generation_id: str):
        """Analyze WebSocket updates received during generation"""
        logger.info("Analyzing WebSocket updates...")
        
        generation_updates = [
            update for update in self.generation_updates 
            if update["data"].get("data", {}).get("generation_id") == generation_id or
               update["data"].get("generation_id") == generation_id
        ]
        
        if not generation_updates:
            logger.warning("No WebSocket updates received for generation")
            self.test_results["websocket_analysis"] = {"status": "fail", "error": "No updates received"}
            return
        
        # Analyze update types
        update_types = [update["type"] for update in generation_updates]
        unique_types = set(update_types)
        
        # Check for expected update sequence
        expected_types = {"generation_progress", "generation_state"}
        has_progress = "generation_progress" in unique_types
        has_state = "generation_state" in unique_types
        has_complete = "generation_complete" in unique_types
        has_error = "generation_error" in unique_types
        
        logger.info(f"WebSocket update analysis:")
        logger.info(f"  - Total updates: {len(generation_updates)}")
        logger.info(f"  - Update types: {unique_types}")
        logger.info(f"  - Has progress updates: {has_progress}")
        logger.info(f"  - Has state updates: {has_state}")
        logger.info(f"  - Has completion: {has_complete}")
        logger.info(f"  - Has errors: {has_error}")
        
        # Timeline analysis
        if generation_updates:
            first_update = generation_updates[0]["timestamp"]
            last_update = generation_updates[-1]["timestamp"]
            logger.info(f"  - First update: {first_update}")
            logger.info(f"  - Last update: {last_update}")
        
        self.test_results["websocket_analysis"] = {
            "status": "pass" if (has_progress or has_state) else "fail",
            "total_updates": len(generation_updates),
            "update_types": list(unique_types),
            "has_progress": has_progress,
            "has_state": has_state,
            "has_complete": has_complete,
            "has_error": has_error,
            "updates": generation_updates
        }
    
    async def run_full_test(self):
        """Run complete end-to-end test"""
        logger.info("Starting end-to-end generation system test...")
        
        try:
            # 1. Test API health
            if not self.test_api_health():
                logger.error("API health check failed, aborting test")
                return False
            
            # 2. Connect WebSocket
            await self.connect_websocket()
            await asyncio.sleep(1)  # Give WebSocket time to establish
            
            # 3. Start generation
            generation_id = self.start_generation()
            if not generation_id:
                logger.error("Failed to start generation, aborting test")
                return False
            
            # 4. Monitor generation progress
            success = await self.monitor_generation(generation_id, timeout=300)
            
            # 5. Analyze WebSocket updates
            await asyncio.sleep(2)  # Allow final updates to arrive
            self.analyze_websocket_updates(generation_id)
            
            # 6. Generate test report
            self.generate_test_report()
            
            return success
            
        except Exception as e:
            logger.error(f"Test execution error: {e}")
            self.test_results["test_execution"] = {"status": "error", "error": str(e)}
            return False
        
        finally:
            if self.websocket:
                await self.websocket.close()
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        logger.info("Generating test report...")
        
        report = {
            "test_timestamp": datetime.now().isoformat(),
            "test_summary": {
                "total_tests": len(self.test_results),
                "passed": len([r for r in self.test_results.values() if r.get("status") == "pass"]),
                "failed": len([r for r in self.test_results.values() if r.get("status") == "fail"]),
                "errors": len([r for r in self.test_results.values() if r.get("status") == "error"])
            },
            "test_results": self.test_results,
            "configuration": {
                "api_base_url": self.api_base_url,
                "ws_url": self.ws_url
            }
        }
        
        # Save report to file
        report_filename = f"generation_test_report_{int(time.time())}.json"
        with open(report_filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Test report saved to: {report_filename}")
        
        # Print summary
        summary = report["test_summary"]
        logger.info(f"Test Summary: {summary['passed']}/{summary['total_tests']} passed")
        
        for test_name, result in self.test_results.items():
            status = result.get("status", "unknown")
            logger.info(f"  - {test_name}: {status.upper()}")

async def main():
    """Main test execution"""
    tester = GenerationSystemTester()
    
    logger.info("🚀 Starting ComfyUI Generation System End-to-End Test")
    logger.info("=" * 60)
    
    success = await tester.run_full_test()
    
    logger.info("=" * 60)
    if success:
        logger.info("✅ End-to-end test completed successfully!")
    else:
        logger.info("❌ End-to-end test failed!")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
