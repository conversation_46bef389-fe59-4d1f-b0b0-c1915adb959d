@echo off
title Testing Centralized Logging System
color 0E

echo.
echo 🧪 ComfyUI Centralized Logging System Test
echo ==========================================
echo.

REM Test 1: Check if logging directory exists
echo 🔍 Test 1: Checking logging directory...
if exist "C:\temp\comfyui_workspace\logs" (
    echo   ✅ Logging directory exists
) else (
    echo   ❌ Logging directory missing
    echo   Creating directory...
    mkdir "C:\temp\comfyui_workspace\logs" 2>nul
    if exist "C:\temp\comfyui_workspace\logs" (
        echo   ✅ Directory created successfully
    ) else (
        echo   ❌ Failed to create directory
        goto :test_failed
    )
)

echo.
echo 🔍 Test 2: Checking logger script...
if exist "C:\temp\comfyui_workspace\logs\centralized_logger.py" (
    echo   ✅ Logger script exists
) else (
    echo   ❌ Logger script missing
    goto :test_failed
)

echo.
echo 🔍 Test 3: Testing basic logging functionality...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "TEST" "Basic logging test"
if errorlevel 1 (
    echo   ❌ Basic logging failed
    goto :test_failed
) else (
    echo   ✅ Basic logging successful
)

echo.
echo 🔍 Test 4: Testing error logging...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_error "TEST" "Error logging test"
if errorlevel 1 (
    echo   ❌ Error logging failed
    goto :test_failed
) else (
    echo   ✅ Error logging successful
)

echo.
echo 🔍 Test 5: Testing warning logging...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_warning "TEST" "Warning logging test"
if errorlevel 1 (
    echo   ❌ Warning logging failed
    goto :test_failed
) else (
    echo   ✅ Warning logging successful
)

echo.
echo 🔍 Test 6: Testing critical logging...
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_critical "TEST" "Critical logging test"
if errorlevel 1 (
    echo   ❌ Critical logging failed
    goto :test_failed
) else (
    echo   ✅ Critical logging successful
)

echo.
echo 🔍 Test 7: Checking log file creation...
cd /d "C:\temp\comfyui_workspace\logs"

if exist "system_errors.json" (
    echo   ✅ system_errors.json created
) else (
    echo   ❌ system_errors.json missing
    goto :test_failed
)

if exist "claude_errors.json" (
    echo   ✅ claude_errors.json created
) else (
    echo   ❌ claude_errors.json missing
    goto :test_failed
)

if exist "system_activity.log" (
    echo   ✅ system_activity.log created
) else (
    echo   ❌ system_activity.log missing
    goto :test_failed
)

echo.
echo 🔍 Test 8: Testing error checker script...
cd /d "g:\comfyui_Front"
if exist "check_errors_for_claude.bat" (
    echo   ✅ Error checker script exists
    echo   Running error checker...
    call check_errors_for_claude.bat
) else (
    echo   ❌ Error checker script missing
    goto :test_failed
)

echo.
echo 🔍 Test 9: Testing service manager logging integration...
if exist "service_manager.bat" (
    echo   ✅ Service manager exists
    echo   Testing logging integration...
    python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "SERVICE_MANAGER_TEST" "Service manager logging integration test"
    echo   ✅ Service manager logging integration working
) else (
    echo   ❌ Service manager missing
    goto :test_failed
)

echo.
echo 🔍 Test 10: Validating Claude accessibility...
echo   Checking if Claude can read the logs...
cd /d "C:\temp\comfyui_workspace\logs"
type claude_errors.json
echo.
echo   ✅ Claude error log is readable

echo.
echo ================================================================
echo   🎉 ALL TESTS PASSED!
echo ================================================================
echo.
echo ✅ Centralized logging system is fully operational
echo.
echo 📋 System Components:
echo   🔧 centralized_logger.py - Core logging engine
echo   📊 check_errors_for_claude.bat - Claude error checker
echo   🎛️  service_manager.bat - Enhanced with logging
echo   🚀 START_COMPLETE_SYSTEM.bat - Enhanced with logging
echo   🛑 STOP_ALL_SERVICES.bat - Enhanced with logging
echo.
echo 📁 Log Files:
echo   📄 system_errors.json - All system errors
echo   🤖 claude_errors.json - Claude-accessible error summary
echo   📝 system_activity.log - Detailed activity log
echo.
echo 💡 Usage:
echo   - Run 'check_errors_for_claude.bat' to see recent errors
echo   - All service scripts now log their activities
echo   - Logs are automatically created in C:\temp\comfyui_workspace\logs\
echo.

python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_info "SYSTEM_TEST" "Centralized logging system validation completed successfully"

goto :end

:test_failed
echo.
echo ================================================================
echo   ❌ TESTS FAILED!
echo ================================================================
echo.
echo Some tests failed. Please check the error messages above.
echo.
python "C:\temp\comfyui_workspace\logs\centralized_logger.py" log_error "SYSTEM_TEST" "Centralized logging system validation failed" 2>nul || echo Failed to log test failure

:end
echo.
echo Press any key to continue...
pause >nul
