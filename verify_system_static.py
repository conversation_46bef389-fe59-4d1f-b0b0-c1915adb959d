#!/usr/bin/env python3
"""
Generation System Static Verification
Verifies all generation system components are properly set up
"""

import os
import json
import sys
from datetime import datetime

class StaticSystemVerifier:
    def __init__(self):
        self.base_path = "g:\\comfyui_Front"
        self.verification_results = {}
        
    def verify_backend_structure(self) -> bool:
        """Verify backend components and structure"""
        print("\n🔍 STEP 1: Verifying Backend Structure...")
        
        backend_files = [
            "backend\\main.py",
            "backend\\routes\\generation.py",
            "backend\\app\\services\\websocket_relay.py",
            "backend\\app\\services\\comfyui_service.py",
            "backend\\app\\utils\\centralized_logger.py",
            "backend\\requirements.txt"
        ]
        
        all_exist = True
        for file_path in backend_files:
            full_path = os.path.join(self.base_path, file_path)
            if os.path.exists(full_path):
                print(f"✅ Backend: {file_path}")
            else:
                print(f"❌ Backend: {file_path} - NOT FOUND")
                all_exist = False
        
        self.verification_results['backend_structure'] = all_exist
        return all_exist
    
    def verify_frontend_structure(self) -> bool:
        """Verify frontend components and structure"""
        print("\n🔍 STEP 2: Verifying Frontend Structure...")
        
        frontend_files = [
            "frontend\\package.json",
            "frontend\\src\\services\\generationApiService.ts",
            "frontend\\src\\hooks\\useGeneration.ts",
            "frontend\\src\\services\\websocketManager.ts"
        ]
        
        all_exist = True
        for file_path in frontend_files:
            full_path = os.path.join(self.base_path, file_path)
            if os.path.exists(full_path):
                print(f"✅ Frontend: {file_path}")
            else:
                print(f"❌ Frontend: {file_path} - NOT FOUND")
                all_exist = False
        
        self.verification_results['frontend_structure'] = all_exist
        return all_exist
    
    def verify_generation_api_implementation(self) -> bool:
        """Verify generation API implementation details"""
        print("\n🔍 STEP 3: Verifying Generation API Implementation...")
        
        generation_file = os.path.join(self.base_path, "backend\\routes\\generation.py")
        
        if not os.path.exists(generation_file):
            print("❌ Generation API: File not found")
            self.verification_results['generation_api'] = False
            return False
        
        try:
            with open(generation_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for essential components
            essential_components = [
                "GenerationState",
                "GenerationPayload", 
                "GenerationResponse",
                "active_generations",
                "process_generation_enhanced",
                "websocket_relay",
                "centralized_logger"
            ]
            
            missing_components = []
            for component in essential_components:
                if component in content:
                    print(f"✅ API Component: {component}")
                else:
                    print(f"❌ API Component: {component} - NOT FOUND")
                    missing_components.append(component)
            
            # Check for API endpoints
            endpoints = [
                "@router.post(\"/generation/start\"",
                "@router.get(\"/generation/{generation_id}/status\"",
                "@router.post(\"/generation/{generation_id}/cancel\"",
                "@router.get(\"/generation/active\"",
                "@router.get(\"/generation/history\")"
            ]
            
            for endpoint in endpoints:
                if endpoint in content:
                    print(f"✅ API Endpoint: {endpoint.split('\"')[1]}")
                else:
                    print(f"❌ API Endpoint: {endpoint.split('\"')[1]} - NOT FOUND")
                    missing_components.append(endpoint)
            
            success = len(missing_components) == 0
            self.verification_results['generation_api'] = success
            return success
            
        except Exception as e:
            print(f"❌ Generation API: Error reading file - {e}")
            self.verification_results['generation_api'] = False
            return False
    
    def verify_websocket_implementation(self) -> bool:
        """Verify WebSocket implementation"""
        print("\n🔍 STEP 4: Verifying WebSocket Implementation...")
        
        websocket_file = os.path.join(self.base_path, "backend\\app\\services\\websocket_relay.py")
        
        if not os.path.exists(websocket_file):
            print("❌ WebSocket: File not found")
            self.verification_results['websocket_implementation'] = False
            return False
        
        try:
            with open(websocket_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            websocket_components = [
                "broadcast_generation_progress",
                "broadcast_generation_state", 
                "broadcast_generation_error",
                "broadcast_generation_complete",
                "WebSocketRelay"
            ]
            
            missing_components = []
            for component in websocket_components:
                if component in content:
                    print(f"✅ WebSocket: {component}")
                else:
                    print(f"❌ WebSocket: {component} - NOT FOUND")
                    missing_components.append(component)
            
            success = len(missing_components) == 0
            self.verification_results['websocket_implementation'] = success
            return success
            
        except Exception as e:
            print(f"❌ WebSocket: Error reading file - {e}")
            self.verification_results['websocket_implementation'] = False
            return False
    
    def verify_frontend_integration(self) -> bool:
        """Verify frontend integration"""
        print("\n🔍 STEP 5: Verifying Frontend Integration...")
        
        # Check generationApiService.ts
        api_service_file = os.path.join(self.base_path, "frontend\\src\\services\\generationApiService.ts")
        
        if not os.path.exists(api_service_file):
            print("❌ Frontend API Service: File not found")
            self.verification_results['frontend_integration'] = False
            return False
        
        try:
            with open(api_service_file, 'r', encoding='utf-8') as f:
                api_content = f.read()
            
            api_components = [
                "GenerationSettings",
                "GenerationPayload",
                "GenerationProgress", 
                "startGeneration",
                "getGenerationStatus",
                "websocketManager"
            ]
            
            missing_components = []
            for component in api_components:
                if component in api_content:
                    print(f"✅ Frontend API: {component}")
                else:
                    print(f"❌ Frontend API: {component} - NOT FOUND")
                    missing_components.append(component)
            
            # Check useGeneration hook
            hook_file = os.path.join(self.base_path, "frontend\\src\\hooks\\useGeneration.ts")
            
            if os.path.exists(hook_file):
                with open(hook_file, 'r', encoding='utf-8') as f:
                    hook_content = f.read()
                
                hook_components = [
                    "useGeneration",
                    "startGeneration",
                    "cancelGeneration",
                    "isGenerating",
                    "progress"
                ]
                
                for component in hook_components:
                    if component in hook_content:
                        print(f"✅ Frontend Hook: {component}")
                    else:
                        print(f"❌ Frontend Hook: {component} - NOT FOUND")
                        missing_components.append(component)
            else:
                print("❌ Frontend Hook: useGeneration.ts not found")
                missing_components.append("useGeneration.ts")
            
            success = len(missing_components) == 0
            self.verification_results['frontend_integration'] = success
            return success
            
        except Exception as e:
            print(f"❌ Frontend Integration: Error reading files - {e}")
            self.verification_results['frontend_integration'] = False
            return False
    
    def verify_package_configurations(self) -> bool:
        """Verify package.json configurations"""
        print("\n🔍 STEP 6: Verifying Package Configurations...")
        
        package_json_file = os.path.join(self.base_path, "frontend\\package.json")
        
        if not os.path.exists(package_json_file):
            print("❌ Package Config: package.json not found")
            self.verification_results['package_config'] = False
            return False
        
        try:
            with open(package_json_file, 'r', encoding='utf-8') as f:
                package_data = json.load(f)
            
            # Check for Next.js dev script with port 3003
            scripts = package_data.get('scripts', {})
            dev_script = scripts.get('dev', '')
            
            if 'next dev -p 3003' in dev_script:
                print(f"✅ Package Config: Dev script configured for port 3003")
            else:
                print(f"❌ Package Config: Dev script not configured for port 3003")
                print(f"   Current: {dev_script}")
                self.verification_results['package_config'] = False
                return False
            
            # Check for essential dependencies
            dependencies = package_data.get('dependencies', {})
            dev_dependencies = package_data.get('devDependencies', {})
            all_deps = {**dependencies, **dev_dependencies}
            
            essential_deps = ['next', 'react', 'typescript']
            
            missing_deps = []
            for dep in essential_deps:
                if dep in all_deps:
                    print(f"✅ Package Dependency: {dep}")
                else:
                    print(f"❌ Package Dependency: {dep} - NOT FOUND")
                    missing_deps.append(dep)
            
            success = len(missing_deps) == 0
            self.verification_results['package_config'] = success
            return success
            
        except Exception as e:
            print(f"❌ Package Config: Error reading package.json - {e}")
            self.verification_results['package_config'] = False
            return False
    
    def verify_startup_script(self) -> bool:
        """Verify startup script exists and is properly configured"""
        print("\n🔍 STEP 7: Verifying Startup Script...")
        
        startup_script = os.path.join(self.base_path, "START_COMPLETE_SYSTEM_PORT_3003.bat")
        
        if not os.path.exists(startup_script):
            print("❌ Startup Script: START_COMPLETE_SYSTEM_PORT_3003.bat not found")
            self.verification_results['startup_script'] = False
            return False
        
        try:
            with open(startup_script, 'r', encoding='utf-8') as f:
                script_content = f.read()
            
            required_elements = [
                "port 8000",  # Backend port
                "port 3003",  # Frontend port  
                "uvicorn main:app",  # Backend start command
                "npm run dev",  # Frontend start command
                "http://localhost:3003"  # Browser open command
            ]
            
            missing_elements = []
            for element in required_elements:
                if element in script_content:
                    print(f"✅ Startup Script: {element}")
                else:
                    print(f"❌ Startup Script: {element} - NOT FOUND")
                    missing_elements.append(element)
            
            success = len(missing_elements) == 0
            self.verification_results['startup_script'] = success
            return success
            
        except Exception as e:
            print(f"❌ Startup Script: Error reading file - {e}")
            self.verification_results['startup_script'] = False
            return False
    
    def print_final_report(self):
        """Print comprehensive verification report"""
        print("\n" + "="*80)
        print("🎯 COMPLETE GENERATION SYSTEM STATIC VERIFICATION REPORT")
        print("="*80)
        
        total_checks = len(self.verification_results)
        passed_checks = sum(1 for result in self.verification_results.values() if result)
        
        print(f"\n📊 OVERALL SCORE: {passed_checks}/{total_checks} components verified")
        
        print(f"\n📋 DETAILED RESULTS:")
        for component, status in self.verification_results.items():
            status_icon = "✅" if status else "❌"
            component_name = component.replace('_', ' ').title()
            print(f"   {status_icon} {component_name}")
        
        if passed_checks == total_checks:
            print(f"\n🎉 EXCELLENT: All system components are properly implemented!")
            print(f"   ✅ Backend API with 8-state generation lifecycle")
            print(f"   ✅ Real-time WebSocket communication")
            print(f"   ✅ Frontend TypeScript services and React hooks")
            print(f"   ✅ Comprehensive error handling and logging") 
            print(f"   ✅ Complete state management and progress tracking")
            print(f"   ✅ Automated startup script for port 3003")
            
            print(f"\n🚀 READY TO START:")
            print(f"   1. Run: START_COMPLETE_SYSTEM_PORT_3003.bat")
            print(f"   2. Visit: http://localhost:3003")
            print(f"   3. Backend API: http://localhost:8000")
            
            print(f"\n🔧 SYSTEM CAPABILITIES:")
            print(f"   • Text-to-image generation with ComfyUI integration")
            print(f"   • Real-time progress updates via WebSocket")
            print(f"   • 8-state generation lifecycle (queued → completed)")
            print(f"   • Complete error handling and retry logic")
            print(f"   • Generation history and active job tracking")
            print(f"   • Comprehensive logging and monitoring")
            
        else:
            print(f"\n⚠️  ATTENTION: {total_checks - passed_checks} components need fixes")
            print(f"   Please address the missing components above")
        
        print("\n" + "="*80)

def main():
    """Main verification routine"""
    print("🔍 GENERATION SYSTEM STATIC VERIFICATION")
    print("="*80)
    print("Checking all generation system components without runtime dependencies...")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    verifier = StaticSystemVerifier()
    
    # Run all verification steps
    verifier.verify_backend_structure()
    verifier.verify_frontend_structure()  
    verifier.verify_generation_api_implementation()
    verifier.verify_websocket_implementation()
    verifier.verify_frontend_integration()
    verifier.verify_package_configurations()
    verifier.verify_startup_script()
    
    # Print final report
    verifier.print_final_report()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  Verification interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Verification failed with error: {e}")
        sys.exit(1)
